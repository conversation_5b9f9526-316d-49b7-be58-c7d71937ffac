name: 'Close Pull Requests'

on:
  pull_request_target:
    types: opened
  workflow_dispatch:

jobs:
  close:
    # Avoid forks to run this job
    if: github.repository_owner == 'moodle'
    name: Close Pull Requests
    runs-on: ubuntu-latest
    steps:
      - uses: dessant/repo-lockdown@v4
        with:
          process-only: prs
          close-pr: true
          skip-closed-pr-comment: true
          pr-comment: >
             **Please, don't open pull requests via GitHub.**


             This repository (moodle.git) at GitHub is just a mirror of the official Moodle repository.


             For how to contribute patches for Moodle see
             [CONTRIBUTING.md](https://github.com/moodle/moodle/blob/main/CONTRIBUTING.md).


             Closing this pull request.
