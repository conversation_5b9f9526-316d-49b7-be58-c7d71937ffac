name: Web Installer Testing

on:
  workflow_dispatch:

jobs:
  InstallationTest:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]
        php: [8.2, 8.4]
    steps:
      - name: Construct repository URL
        run: echo "REPOSITORY_URL=${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}" >> $GITHUB_ENV
        shell: bash

      - uses: moodlehq/moodle-webinstaller-test@main
        with:
          repository: ${{ env.REPOSITORY_URL }}
          branch: ${{ github.ref_name }}
          php: ${{ matrix.php }}
