{"version": 3, "file": "bulk_user_actions.min.js", "sources": ["../src/bulk_user_actions.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Add bulk actions to the users list report\n *\n * @module     core_admin/bulk_user_actions\n * @copyright  2024 Marina Glancy\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport * as reportSelectors from 'core_reportbuilder/local/selectors';\nimport * as tableEvents from 'core_table/local/dynamic/events';\nimport * as <PERSON><PERSON><PERSON><PERSON><PERSON>hecker from 'core_form/changechecker';\nimport * as CustomEvents from 'core/custom_interaction_events';\nimport jQuery from 'jquery';\n\nconst Selectors = {\n    bulkActionsForm: 'form#user-bulk-action-form',\n    userReportWrapper: '[data-region=\"report-user-list-wrapper\"]',\n    checkbox: 'input[type=\"checkbox\"][data-togglegroup=\"report-select-all\"][data-toggle=\"target\"]',\n    togglerCheckbox: 'input[type=\"checkbox\"][data-togglegroup=\"report-select-all\"][data-toggle=\"toggler\"]',\n    checkedRows: '[data-togglegroup=\"report-select-all\"][data-toggle=\"target\"]:checked',\n};\n\n/**\n * Initialise module\n */\nexport const init = () => {\n\n    const userBulkForm = document.querySelector(Selectors.bulkActionsForm);\n    const userReport = userBulkForm?.closest(Selectors.userReportWrapper)?.querySelector(reportSelectors.regions.report);\n    if (!userBulkForm || !userReport) {\n        return;\n    }\n    const actionSelect = userBulkForm.querySelector('select');\n    CustomEvents.define(actionSelect, [CustomEvents.events.accessibleChange]);\n\n    jQuery(actionSelect).on(CustomEvents.events.accessibleChange, event => {\n        if (event.target.value && `${event.target.value}` !== \"0\") {\n            const e = new Event('submit', {cancelable: true});\n            userBulkForm.dispatchEvent(e);\n            if (!e.defaultPrevented) {\n                FormChangeChecker.markFormSubmitted(userBulkForm);\n                userBulkForm.submit();\n            }\n        }\n    });\n\n    // Every time the checkboxes in the report are changed, update the list of users in the form values\n    // and enable/disable the action select.\n    const updateUserIds = () => {\n        const selectedUsers = [...userReport.querySelectorAll(Selectors.checkedRows)];\n        const selectedUserIds = selectedUsers.map(check => parseInt(check.value));\n        userBulkForm.querySelector('[name=\"userids\"]').value = selectedUserIds.join(',');\n\n        // Disable the action selector if nothing selected, and reset the current selection.\n        actionSelect.disabled = selectedUsers.length === 0;\n        if (actionSelect.disabled) {\n            actionSelect.value = \"0\";\n        }\n\n        const selectedUsersNames = selectedUsers.map(check => document.querySelector(`label[for=\"${check.id}\"]`).textContent);\n        // Add the user ids and names to the form data attributes so they can be available from the\n        // other JS modules that listen to the form submit event.\n        userBulkForm.data = {userids: selectedUserIds, usernames: selectedUsersNames};\n    };\n\n    updateUserIds();\n\n    document.addEventListener('change', event => {\n        // When checkboxes are checked next to individual users or the toggler toggle (Select all/none).\n        if ((event.target.matches(Selectors.checkbox) || event.target.matches(Selectors.togglerCheckbox))\n                && userReport.contains(event.target)) {\n            updateUserIds();\n        }\n    });\n\n    document.addEventListener(tableEvents.tableContentRefreshed, event => {\n        // When the report contents is updated (i.e. page is changed, filters applied, etc).\n        if (userReport.contains(event.target)) {\n            updateUserIds();\n        }\n    });\n};\n"], "names": ["Selectors", "userBulkForm", "document", "querySelector", "userReport", "closest", "_userBulkForm$closest", "reportSelectors", "regions", "report", "actionSelect", "CustomEvents", "define", "events", "accessibleChange", "on", "event", "target", "value", "e", "Event", "cancelable", "dispatchEvent", "defaultPrevented", "FormChangeChecker", "markFormSubmitted", "submit", "updateUserIds", "selectedUsers", "querySelectorAll", "selectedUserIds", "map", "check", "parseInt", "join", "disabled", "length", "selectedUsersNames", "id", "textContent", "data", "userids", "usernames", "addEventListener", "matches", "contains", "tableEvents", "tableContentRefreshed"], "mappings": ";;;;;;;0WA6BMA,0BACe,6BADfA,4BAEiB,2CAFjBA,mBAGQ,qFAHRA,0BAIe,sFAJfA,sBAKW,qFAMG,qCAEVC,aAAeC,SAASC,cAAcH,2BACtCI,WAAaH,MAAAA,4CAAAA,aAAcI,QAAQL,qEAAtBM,sBAAoDH,cAAcI,gBAAgBC,QAAQC,YACxGR,eAAiBG,wBAGhBM,aAAeT,aAAaE,cAAc,UAChDQ,aAAaC,OAAOF,aAAc,CAACC,aAAaE,OAAOC,uCAEhDJ,cAAcK,GAAGJ,aAAaE,OAAOC,kBAAkBE,WACtDA,MAAMC,OAAOC,OAAqC,MAA5B,UAAGF,MAAMC,OAAOC,OAAiB,OACjDC,EAAI,IAAIC,MAAM,SAAU,CAACC,YAAY,IAC3CpB,aAAaqB,cAAcH,GACtBA,EAAEI,mBACHC,kBAAkBC,kBAAkBxB,cACpCA,aAAayB,oBAOnBC,cAAgB,WACZC,cAAgB,IAAIxB,WAAWyB,iBAAiB7B,wBAChD8B,gBAAkBF,cAAcG,KAAIC,OAASC,SAASD,MAAMd,SAClEjB,aAAaE,cAAc,oBAAoBe,MAAQY,gBAAgBI,KAAK,KAG5ExB,aAAayB,SAAoC,IAAzBP,cAAcQ,OAClC1B,aAAayB,WACbzB,aAAaQ,MAAQ,WAGnBmB,mBAAqBT,cAAcG,KAAIC,OAAS9B,SAASC,mCAA4B6B,MAAMM,UAAQC,cAGzGtC,aAAauC,KAAO,CAACC,QAASX,gBAAiBY,UAAWL,qBAG9DV,gBAEAzB,SAASyC,iBAAiB,UAAU3B,SAE3BA,MAAMC,OAAO2B,QAAQ5C,qBAAuBgB,MAAMC,OAAO2B,QAAQ5C,6BAC3DI,WAAWyC,SAAS7B,MAAMC,SACjCU,mBAIRzB,SAASyC,iBAAiBG,YAAYC,uBAAuB/B,QAErDZ,WAAWyC,SAAS7B,MAAMC,SAC1BU"}