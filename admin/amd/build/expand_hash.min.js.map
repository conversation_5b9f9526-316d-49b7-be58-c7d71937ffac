{"version": 3, "file": "expand_hash.min.js", "sources": ["../src/expand_hash.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Expand the collapse section element.\n *\n * @module      core_admin/expand_hash\n * @copyright   Meirza <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n * @since       4.5\n */\n\nconst SELECTORS = {\n    COLLAPSE_ELEMENTS: '[data-bs-toggle=\"collapse\"]',\n    FOCUSTHENEXPAND_ELEMENTS: '.focus-expand',\n};\n\n/**\n * Initializes the focus and expand functionality.\n */\nexport const init = () => {\n    // Select all collapsible elements only.\n    const focusexpand = document.querySelector(SELECTORS.FOCUSTHENEXPAND_ELEMENTS);\n\n    // Add click event listener to the anchor element\n    focusexpand?.addEventListener('click', () => {\n        expandSection(`${focusexpand.getAttribute('href')}`);\n    });\n};\n\n/**\n * Expands a section based on the provided URL hash.\n *\n * This function takes a hash string, finds the corresponding element in the DOM,\n * and expands it if it is currently collapsed. It updates the necessary ARIA\n * attributes and classes to reflect the expanded state.\n *\n * @param {string} hash - The hash (e.g., '#elementId') of the element to expand.\n */\nexport const expandSection = (hash) => {\n    const container = document.querySelector(hash);\n    const targetContainer = container?.querySelector(SELECTORS.COLLAPSE_ELEMENTS);\n\n    if (targetContainer?.getAttribute('aria-expanded') === 'false') {\n        const collapseId = targetContainer.getAttribute('aria-controls');\n        const collapseContainer = document.getElementById(collapseId);\n\n        // Show the content.\n        collapseContainer.classList.remove('collapse');\n        collapseContainer.classList.add('show');\n\n        // Update aria-expanded attribute to reflect the new state.\n        targetContainer.setAttribute('aria-expanded', 'true');\n        targetContainer.classList.remove('collapsed');\n\n        // Get collapse expand menu element.\n        const collapseElement = document.querySelector('.collapseexpand.collapsemenu');\n        // Ensure it gets noticed to make it work.\n        collapseElement.setAttribute('aria-expanded', 'true');\n        collapseElement.classList.remove('collapsed');\n    }\n};\n"], "names": ["SELECTORS", "focusexpand", "document", "querySelector", "addEventListener", "expandSection", "getAttribute", "hash", "container", "targetContainer", "collapseId", "collapseContainer", "getElementById", "classList", "remove", "add", "setAttribute", "collapseElement"], "mappings": ";;;;;;;;;MAwBMA,4BACiB,8BADjBA,mCAEwB,8BAMV,WAEVC,YAAcC,SAASC,cAAcH,oCAG3CC,MAAAA,aAAAA,YAAaG,iBAAiB,SAAS,KACnCC,wBAAiBJ,YAAYK,aAAa,oBAarCD,cAAiBE,aACpBC,UAAYN,SAASC,cAAcI,MACnCE,gBAAkBD,MAAAA,iBAAAA,UAAWL,cAAcH,gCAEM,WAAnDS,MAAAA,uBAAAA,gBAAiBH,aAAa,kBAA8B,OACtDI,WAAaD,gBAAgBH,aAAa,iBAC1CK,kBAAoBT,SAASU,eAAeF,YAGlDC,kBAAkBE,UAAUC,OAAO,YACnCH,kBAAkBE,UAAUE,IAAI,QAGhCN,gBAAgBO,aAAa,gBAAiB,QAC9CP,gBAAgBI,UAAUC,OAAO,mBAG3BG,gBAAkBf,SAASC,cAAc,gCAE/Cc,gBAAgBD,aAAa,gBAAiB,QAC9CC,gBAAgBJ,UAAUC,OAAO"}