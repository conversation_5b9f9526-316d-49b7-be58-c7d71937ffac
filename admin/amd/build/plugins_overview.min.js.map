{"version": 3, "file": "plugins_overview.min.js", "sources": ["../src/plugins_overview.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Allows to filter the plugin list on plugins overview page\n *\n * @module     core_admin/plugins_overview\n * @copyright  2024 Marina Glancy\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nconst SELECTORS = {\n    PLUGIN_FILTERS: '#plugins-overview-panel [data-filterby]',\n    PLUGIN_ROWS: 'table#plugins-control-panel tbody tr:not(.plugintypeheader)',\n    PLUGIN_TYPE_ROWS: 'table#plugins-control-panel tbody tr.plugintypeheader',\n};\n\n/**\n * Initialise filters for the \"Plugins overview\" page\n */\nexport function init() {\n    const filters = document.querySelectorAll(SELECTORS.PLUGIN_FILTERS);\n    const pluginRows = document.querySelectorAll(SELECTORS.PLUGIN_ROWS);\n    const pluginTypeRows = document.querySelectorAll(SELECTORS.PLUGIN_TYPE_ROWS);\n\n    const filterPlugins = (target) => {\n        const filterBy = target.getAttribute('data-filterby');\n        const headerVisibility = {};\n\n        // Hide all plugin rows in the plugin table that do not match the filter and show all others.\n        for (const row of pluginRows) {\n            const type = [...row.classList].find(s => s.startsWith('type-'));\n            const visible = filterBy === 'all' ? true : row.classList.contains(filterBy);\n            row.style.display = visible ? null : 'none';\n            if (visible && type) {\n                headerVisibility[type] = true;\n            }\n        }\n\n        // Hide all the plugin type headers that do not have any visible plugins and show all others.\n        for (const row of pluginTypeRows) {\n            const type = [...row.classList].find(s => s.startsWith('type-'));\n            if (type) {\n                const visible = filterBy === 'all' || headerVisibility[type];\n                row.style.display = visible ? null : 'none';\n            }\n        }\n\n        // Toggle 'active' class for the selected filter.\n        filters.forEach(el => el.classList.remove('active'));\n        target.classList.add('active');\n    };\n\n    // Add event listeners for the links changing plugins filters.\n    filters\n    .forEach(target => target.addEventListener('click', (e) => {\n        e.preventDefault();\n        window.history.replaceState({}, null, e.target.href);\n        filterPlugins(target);\n    }));\n\n    // Pre-filter plugins based on the current url anchor.\n    if (window.location.hash.length > 1) {\n        const anchor = window.location.hash.substring(1);\n        const target = [...filters].find(t => t.getAttribute('data-filterby') === anchor);\n        if (target) {\n            filterPlugins(target);\n        }\n    }\n}\n"], "names": ["filters", "document", "querySelectorAll", "SELECTORS", "pluginRows", "pluginTypeRows", "filterPlugins", "target", "filterBy", "getAttribute", "headerVisibility", "row", "type", "classList", "find", "s", "startsWith", "visible", "contains", "style", "display", "for<PERSON>ach", "el", "remove", "add", "addEventListener", "e", "preventDefault", "window", "history", "replaceState", "href", "location", "hash", "length", "anchor", "substring", "t"], "mappings": "4JAiCUA,QAAUC,SAASC,iBAAiBC,0BACpCC,WAAaH,SAASC,iBAAiBC,uBACvCE,eAAiBJ,SAASC,iBAAiBC,4BAE3CG,cAAiBC,eACbC,SAAWD,OAAOE,aAAa,iBAC/BC,iBAAmB,OAGpB,MAAMC,OAAOP,WAAY,OACpBQ,KAAO,IAAID,IAAIE,WAAWC,MAAKC,GAAKA,EAAEC,WAAW,WACjDC,QAAuB,QAAbT,UAA4BG,IAAIE,UAAUK,SAASV,UACnEG,IAAIQ,MAAMC,QAAUH,QAAU,KAAO,OACjCA,SAAWL,OACXF,iBAAiBE,OAAQ,OAK5B,MAAMD,OAAON,eAAgB,OACxBO,KAAO,IAAID,IAAIE,WAAWC,MAAKC,GAAKA,EAAEC,WAAW,cACnDJ,KAAM,OACAK,QAAuB,QAAbT,UAAsBE,iBAAiBE,MACvDD,IAAIQ,MAAMC,QAAUH,QAAU,KAAO,QAK7CjB,QAAQqB,SAAQC,IAAMA,GAAGT,UAAUU,OAAO,YAC1ChB,OAAOM,UAAUW,IAAI,cAIzBxB,QACCqB,SAAQd,QAAUA,OAAOkB,iBAAiB,SAAUC,IACjDA,EAAEC,iBACFC,OAAOC,QAAQC,aAAa,GAAI,KAAMJ,EAAEnB,OAAOwB,MAC/CzB,cAAcC,aAIdqB,OAAOI,SAASC,KAAKC,OAAS,EAAG,OAC3BC,OAASP,OAAOI,SAASC,KAAKG,UAAU,GACxC7B,OAAS,IAAIP,SAASc,MAAKuB,GAAKA,EAAE5B,aAAa,mBAAqB0B,SACtE5B,QACAD,cAAcC;;;;;;;;MAvDpBJ,yBACc,0CADdA,sBAEW,8DAFXA,2BAGgB"}