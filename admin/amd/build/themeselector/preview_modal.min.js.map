{"version": 3, "file": "preview_modal.min.js", "sources": ["../../src/themeselector/preview_modal.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Modal for theme previews.\n *\n * @module     core_admin/themeselector/preview_modal\n * @copyright  2023 <PERSON> <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport ModalEvents from 'core/modal_events';\nimport ModalCancel from 'core/modal_cancel';\nimport ModalSaveCancel from 'core/modal_save_cancel';\nimport Notification from 'core/notification';\nimport Templates from 'core/templates';\nimport {getString} from 'core/str';\n\nconst SELECTORS = {\n    THEMES_CONTAINER: 'themelist',\n    PREVIEW: '[data-action=\"preview\"]',\n};\n\n/**\n * Entrypoint of the js.\n *\n * @method init\n */\nexport const init = () => {\n    registerListenerEvents();\n};\n\n/**\n * Register theme related event listeners.\n *\n * @method registerListenerEvents\n */\nconst registerListenerEvents = () => {\n    document.addEventListener('click', (e) => {\n        const preview = e.target.closest(SELECTORS.PREVIEW);\n        if (preview) {\n            buildModal(preview).catch(Notification.exception);\n        }\n    });\n};\n\n/**\n * Build the modal with the provided data.\n *\n * @method buildModal\n * @param {object} element\n */\nconst buildModal = async(element) => {\n\n    // This string can be long. We will fetch it with JS as opposed to passing it as an attribute.\n    let description = await getString('choosereadme', 'theme_' + element.getAttribute('data-choose'));\n\n    const themesContainer = document.getElementById(SELECTORS.THEMES_CONTAINER);\n    const definedInConfig = parseInt(themesContainer.dataset.definedinconfig);\n    // Prepare data for modal.\n    const data = {\n        name: element.getAttribute('data-name'),\n        image: element.getAttribute('data-image'),\n        description: description.replace(/<[^>]+>/g, ' '), // Strip out HTML tags.\n        current: element.getAttribute('data-current'),\n        actionurl: element.getAttribute('data-actionurl'),\n        choose: element.getAttribute('data-choose'),\n        sesskey: element.getAttribute('data-sesskey'),\n        definedinconfig: definedInConfig,\n    };\n\n    // Determine which modal template we should use.\n    let modalTemplate = ModalSaveCancel;\n    if (data.current || data.definedinconfig) {\n        modalTemplate = ModalCancel;\n    }\n\n    const modal = await modalTemplate.create({\n        title: data.name,\n        body: Templates.render('core_admin/themeselector/theme_preview_modal', data),\n        large: true,\n        buttons: {\n            'save': getString('selecttheme', 'moodle'),\n            'cancel': getString('closebuttontitle', 'moodle'),\n        },\n        show: true,\n    });\n\n    modal.getRoot().on(ModalEvents.save, () => {\n        modal.getRoot().find('form').submit();\n    });\n};\n"], "names": ["SELECTORS", "registerListenerEvents", "document", "addEventListener", "e", "preview", "target", "closest", "buildModal", "catch", "Notification", "exception", "async", "description", "element", "getAttribute", "themesC<PERSON>r", "getElementById", "definedInConfig", "parseInt", "dataset", "definedinconfig", "data", "name", "image", "replace", "current", "<PERSON><PERSON>l", "choose", "sesskey", "modalTemplate", "ModalSaveCancel", "ModalCancel", "modal", "create", "title", "body", "Templates", "render", "large", "buttons", "show", "getRoot", "on", "ModalEvents", "save", "find", "submit"], "mappings": ";;;;;;;gWA8BMA,2BACgB,YADhBA,kBAEO,wCAQO,KAChBC,gCAQEA,uBAAyB,KAC3BC,SAASC,iBAAiB,SAAUC,UAC1BC,QAAUD,EAAEE,OAAOC,QAAQP,mBAC7BK,SACAG,WAAWH,SAASI,MAAMC,sBAAaC,eAW7CH,WAAaI,MAAAA,cAGXC,kBAAoB,kBAAU,eAAgB,SAAWC,QAAQC,aAAa,sBAE5EC,gBAAkBd,SAASe,eAAejB,4BAC1CkB,gBAAkBC,SAASH,gBAAgBI,QAAQC,iBAEnDC,KAAO,CACTC,KAAMT,QAAQC,aAAa,aAC3BS,MAAOV,QAAQC,aAAa,cAC5BF,YAAaA,YAAYY,QAAQ,WAAY,KAC7CC,QAASZ,QAAQC,aAAa,gBAC9BY,UAAWb,QAAQC,aAAa,kBAChCa,OAAQd,QAAQC,aAAa,eAC7Bc,QAASf,QAAQC,aAAa,gBAC9BM,gBAAiBH,qBAIjBY,cAAgBC,4BAChBT,KAAKI,SAAWJ,KAAKD,mBACrBS,cAAgBE,6BAGdC,YAAcH,cAAcI,OAAO,CACrCC,MAAOb,KAAKC,KACZa,KAAMC,mBAAUC,OAAO,+CAAgDhB,MACvEiB,OAAO,EACPC,QAAS,OACG,kBAAU,cAAe,kBACvB,kBAAU,mBAAoB,WAE5CC,MAAM,IAGVR,MAAMS,UAAUC,GAAGC,sBAAYC,MAAM,KACjCZ,MAAMS,UAAUI,KAAK,QAAQC"}