{{!
    This file is part of Moodle - http://moodle.org/

    <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_admin\local\settings\autocomplete

    Admin setting auto complete.

    Context variables required for this template:
    * name - element name
    * id - element id
    * options - choices
    * multiple - is multiple choices?
    * manageurl - url to manage choices
    * managetext - string to display manage url
    * tags - Should we allow typing new entries to the field?
    * ajax - Name of an AMD module to send/process ajax requests
    * placeholder - Placeholder text for an empty list
    * casesensitive - Whether the search has to be case-sensitive
    * showsuggestions - Show suggestions by default - but this can be turned off
    * noselectionstring - String that is shown when there are no selections


    Example context (json):
    {
        "name": "name0",
        "id": "id0",
        "options": [{
            "value": "1",
            "text": "option 1",
            "selected": true,
            "disabled": false
        }],
        "multiple": true,
        "manageurl": "",
        "managetext": "",
        "tags": false,
        "ajax": "",
        "placeholder": "",
        "casesensitive": false,
        "showsuggestions": true,
        "noselectionstring": ""
    }
}}
{{!
    Setting autocomplete.
}}

<div class="defaultsnext">
    <input type="hidden" name="{{name}}[xxxxx]" value="_qf__force_multiselect_submission">
    <select class="form-select" name="{{name}}[]"
            id="{{id}}"
            {{#multiple}}multiple{{/multiple}}>
        {{#options}}
            <option value="{{value}}" {{#selected}}selected{{/selected}}>{{{text}}}</option>
        {{/options}}
    </select>
    {{#manageurl}}
        <a href="{{manageurl}}">{{managetext}}</a>
    {{/manageurl}}
</div>
<br/>

{{#js}}
    require(['core/form-autocomplete'], function(module) {
        module.enhance({{#quote}}#{{id}}{{/quote}},
        {{tags}},
        {{#quote}}{{ajax}}{{/quote}},
        {{#quote}}{{placeholder}}{{/quote}},
        {{casesensitive}},
        {{showsuggestions}},
        {{#quote}}{{noselectionstring}}{{/quote}});
    });
{{/js}}
