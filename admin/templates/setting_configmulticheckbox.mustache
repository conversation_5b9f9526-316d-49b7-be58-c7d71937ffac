{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_admin/setting_configmulticheckbox

    Admin multicheckbox setting template.

    Context variables required for this template:
    * name - form element name
    * hasoptions - there are some options
    * options - list of options containing name, key, id, checked, label
    * readonly - bool

    Example context (json):
    {
        "name": "test",
        "hasoptions": true,
        "options": [ { "name": "Option", "key": "k1", "id": "id0", "checked": true, "label": "Option label"} ],
        "readonly": false

    }
}}
<div class="form-multicheckbox">
    {{^readonly}}<input type="hidden" name="{{name}}[xxxxx]" value="1">{{/readonly}}
    {{#hasoptions}}
        <ul>
            {{#options}}
                <li>
                    <input type="checkbox" name="{{name}}[{{key}}]" value="1" id="{{id}}_{{key}}" {{#readonly}}disabled{{/readonly}} {{#checked}}checked{{/checked}}>
                    <label for="{{id}}_{{key}}">{{{label}}}</label>
                </li>
            {{/options}}
        </ul>
    {{/hasoptions}}
</div>
