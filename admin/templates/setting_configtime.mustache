{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_admin/setting_configtime

    Admin time setting template.

    Context variables required for this template:
    * name - form element name
    * id - element id
    * hours - list of valid hour options containing name, value, selected
    * minutes - list of valid minute options containing name, value, selected
    * readonly - bool

    Example context (json):
    {
        "name": "test",
        "id": "test0",
        "readonly": false,
        "minutes": [
            { "name": "00", "value": "0", "selected": true },
            { "name": "01", "value": "1", "selected": false }
        ],
        "hours": [
            { "name": "1", "value": "1", "selected": true },
            { "name": "2", "value": "2", "selected": false }
        ]
    }
}}
{{!
    Setting configtime.
}}
<div class="form-time defaultsnext">
    <div class="d-flex flex-wrap align-items-center text-ltr">
        <label class="visually-hidden" for="{{id}}h">{{#str}}hours{{/str}}</label>
        <select id="{{id}}h" name="{{name}}[h]" class="form-select" {{#readonly}}disabled{{/readonly}}>
            {{#hours}}
                <option value="{{value}}" {{#selected}}selected{{/selected}}>{{name}}</option>
            {{/hours}}
        </select>:
        <label class="visually-hidden" for="{{id}}m">{{#str}}minutes{{/str}}</label>
        <select id="{{id}}m" name="{{name}}[m]" class="form-select" {{#readonly}}disabled{{/readonly}}>
            {{#minutes}}
                <option value="{{value}}" {{#selected}}selected{{/selected}}>{{name}}</option>
            {{/minutes}}
        </select>
    </div>
</div>

