{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_admin/setting_courselist_frontpage

    Admin courselist_frontpage setting template.

    Context variables required for this template:
    * selects list of select objects containing id, name, key and options.
      options is another nested list of items containing name, value and selected

    Example context (json):
    {
        "selects": [
            {
                "id": "i1",
                "name": "s1",
                "key": "k1",
                "options": [
                    { "name": "Fish", "value": "snapper", "selected": true }
                ]
            }
        ]
    }
}}
{{!
    Setting courselist_frontpage.
}}
<div class="mb-3">
    {{#selects}}
        <select id="{{id}}{{key}}" name="{{name}}[]" class="form-select mb-1">
            {{#options}}
                <option value="{{value}}" {{#selected}}selected{{/selected}}>{{name}}</option>
            {{/options}}
        </select>
        <br>
    {{/selects}}
</div>
