{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_admin/setting_emoticons

    Admin emoticons setting template.

    Context variables required for this template:
    * name - form element name
    * id - element id

    Example context (json):
    {
        "emoticons": [
            { "fields": [
                { "name": "Smile", "field": "f1", "value": ":)" }
            ]}
        ]
    }
}}
{{!
    Setting emoticons.
}}
<div class="mb-3">
    <table id="emoticonsetting" class="admintable table generaltable">
        <thead>
            <tr>
                <th>{{#str}}emoticontext, admin{{/str}}</th>
                <th>{{#str}}emoticonimagename, admin{{/str}}</th>
                <th>{{#str}}emoticoncomponent, admin{{/str}}</th>
                <th colspan="2">{{#str}}emoticonalt, admin{{/str}}</th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            {{#emoticons}}
                <tr>
                    {{#fields}}
                        <td class="c{{index}}">
                            <input type="text" name="{{name}}[{{field}}]" class="form-text form-control text-ltr" value="{{value}}">
                        </td>
                    {{/fields}}
                    <td>
                        {{#icon}}
                            {{>core/pix_icon}}
                        {{/icon}}
                    </td>
                </tr>
            {{/emoticons}}
        </tbody>
    </table>
</div>
<div>
    <a href="{{reseturl}}">{{#str}}emoticonsreset, admin{{/str}}</a>
</div>
