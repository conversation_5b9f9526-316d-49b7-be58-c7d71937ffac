{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_admin/webservice_token_new

    This is the template for displaying the newly created webservice token.

    Context variable required for this template:
    * token - Token value

    Example context (json):
    {
        "token": "ef9ee4d0c6eed5eab8453a63b93b5b8b"
    }
}}

<div class="d-inline-block">
    <div class="alert alert-warning">
        <div class="lead">{{#str}}tokennewmessage, webservice{{/str}}</div>
    </div>
    <div class="alert alert-primary">
        <div class="lead">{{tokenname}}</div>
        <div class="d-flex justify-content-start align-middle">
            <div class="lead text-break pt-1" id="copytoclipboardtoken">{{token}}</div>
            <button class="btn btn-primary ms-2" data-action="copytoclipboard" data-clipboard-target="#copytoclipboardtoken" data-clipboard-success-message="{{#str}}tokencopied, webservice{{/str}}">
            {{#pix}}t/copy, core {{/pix}}{{#str}}copytoclipboard{{/str}}</button>
        </div>
    </div>
</div>

{{#js}}
    require(['core/copy_to_clipboard']);
{{/js}}
