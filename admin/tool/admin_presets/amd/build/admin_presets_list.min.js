define("tool_admin_presets/admin_presets_list",["exports","core/event_dispatcher","core/notification","core/prefetch","core/str","core/pending","core_reportbuilder/local/events","core_reportbuilder/local/selectors","core/toast","tool_admin_presets/repository"],(function(_exports,_event_dispatcher,_notification,_prefetch,_str,_pending,reportEvents,reportSelectors,_toast,_repository){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireWildcard(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}return newObj.default=obj,cache&&cache.set(obj,newObj),newObj}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_notification=_interopRequireDefault(_notification),_pending=_interopRequireDefault(_pending),reportEvents=_interopRequireWildcard(reportEvents),reportSelectors=_interopRequireWildcard(reportSelectors);_exports.init=()=>{(0,_prefetch.prefetchStrings)("core",["delete"]),(0,_prefetch.prefetchStrings)("tool_admin_presets",["deleteshow","deletepreset","eventpresetdeleted","deletepreviouslyapplied"]),document.addEventListener("click",(event=>{const presetDelete=event.target.closest('[data-action="admin-preset-delete"]');if(presetDelete){event.preventDefault();const triggerElement=presetDelete.closest(".dropdown").querySelector(".dropdown-toggle"),stringid=presetDelete.dataset.presetRollback?"deletepreviouslyapplied":"deletepreset";_notification.default.saveCancelPromise((0,_str.getString)("deleteshow","tool_admin_presets"),(0,_str.getString)(stringid,"tool_admin_presets",presetDelete.dataset.presetName),(0,_str.getString)("delete","core"),{triggerElement:triggerElement}).then((()=>{const pendingPromise=new _pending.default("tool/admin_presets:deletepreset"),reportElement=event.target.closest(reportSelectors.regions.report);return(0,_repository.deletePreset)(presetDelete.dataset.presetId).then((()=>(0,_toast.add)((0,_str.getString)("eventpresetdeleted","tool_admin_presets")))).then((()=>((0,_event_dispatcher.dispatchEvent)(reportEvents.tableReload,{preservePagination:!0},reportElement),pendingPromise.resolve()))).catch(_notification.default.exception)})).catch((()=>{}))}}))}}));

//# sourceMappingURL=admin_presets_list.min.js.map