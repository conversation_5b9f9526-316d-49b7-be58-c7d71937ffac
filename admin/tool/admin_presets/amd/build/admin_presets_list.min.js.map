{"version": 3, "file": "admin_presets_list.min.js", "sources": ["../src/admin_presets_list.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Admin presets list management\n *\n * @module     tool_admin_presets/admin_presets_list\n * @copyright  2024 David <PERSON> <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n\"use strict\";\n\nimport {dispatchEvent} from 'core/event_dispatcher';\nimport Notification from 'core/notification';\nimport {prefetchStrings} from 'core/prefetch';\nimport {getString} from 'core/str';\nimport Pending from 'core/pending';\nimport * as reportEvents from 'core_reportbuilder/local/events';\nimport * as reportSelectors from 'core_reportbuilder/local/selectors';\nimport {add as addToast} from 'core/toast';\nimport {deletePreset} from 'tool_admin_presets/repository';\n\n/**\n * Initialise module\n */\nexport const init = () => {\n    prefetchStrings('core', [\n        'delete',\n    ]);\n\n    prefetchStrings('tool_admin_presets', [\n        'deleteshow',\n        'deletepreset',\n        'eventpresetdeleted',\n        'deletepreviouslyapplied'\n    ]);\n\n    document.addEventListener('click', event => {\n        const presetDelete = event.target.closest('[data-action=\"admin-preset-delete\"]');\n        if (presetDelete) {\n            event.preventDefault();\n\n            // Use triggerElement to return focus to the action menu toggle.\n            const triggerElement = presetDelete.closest('.dropdown').querySelector('.dropdown-toggle');\n            const stringid = presetDelete.dataset.presetRollback ? 'deletepreviouslyapplied' : 'deletepreset';\n\n            /* eslint-disable promise/no-nesting */\n            Notification.saveCancelPromise(\n                getString('deleteshow', 'tool_admin_presets'),\n                getString(stringid, 'tool_admin_presets', presetDelete.dataset.presetName),\n                getString('delete', 'core'),\n                {triggerElement}\n            ).then(() => {\n                const pendingPromise = new Pending('tool/admin_presets:deletepreset');\n                const reportElement = event.target.closest(reportSelectors.regions.report);\n\n                return deletePreset(presetDelete.dataset.presetId)\n                    .then(() => addToast(getString('eventpresetdeleted', 'tool_admin_presets')))\n                    .then(() => {\n                        dispatchEvent(reportEvents.tableReload, {preservePagination: true}, reportElement);\n                        return pendingPromise.resolve();\n                    })\n                    .catch(Notification.exception);\n            }).catch(() => {\n                return;\n            });\n        }\n    });\n};\n"], "names": ["document", "addEventListener", "event", "presetDelete", "target", "closest", "preventDefault", "triggerElement", "querySelector", "stringid", "dataset", "presetRollback", "saveCancelPromise", "presetName", "then", "pendingPromise", "Pending", "reportElement", "reportSelectors", "regions", "report", "presetId", "reportEvents", "tableReload", "preservePagination", "resolve", "catch", "Notification", "exception"], "mappings": "+qDAsCoB,mCACA,OAAQ,CACpB,yCAGY,qBAAsB,CAClC,aACA,eACA,qBACA,4BAGJA,SAASC,iBAAiB,SAASC,cACzBC,aAAeD,MAAME,OAAOC,QAAQ,0CACtCF,aAAc,CACdD,MAAMI,uBAGAC,eAAiBJ,aAAaE,QAAQ,aAAaG,cAAc,oBACjEC,SAAWN,aAAaO,QAAQC,eAAiB,0BAA4B,qCAGtEC,mBACT,kBAAU,aAAc,uBACxB,kBAAUH,SAAU,qBAAsBN,aAAaO,QAAQG,aAC/D,kBAAU,SAAU,QACpB,CAACN,eAAAA,iBACHO,MAAK,WACGC,eAAiB,IAAIC,iBAAQ,mCAC7BC,cAAgBf,MAAME,OAAOC,QAAQa,gBAAgBC,QAAQC,eAE5D,4BAAajB,aAAaO,QAAQW,UACpCP,MAAK,KAAM,eAAS,kBAAU,qBAAsB,yBACpDA,MAAK,yCACYQ,aAAaC,YAAa,CAACC,oBAAoB,GAAOP,eAC7DF,eAAeU,aAEzBC,MAAMC,sBAAaC,cACzBF,OAAM"}