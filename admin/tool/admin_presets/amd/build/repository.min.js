define("tool_admin_presets/repository",["exports","core/ajax"],(function(_exports,_ajax){var obj;
/**
   * Module to handle presets AJAX requests
   *
   * @module     tool_admin_presets/repository
   * @copyright  2024 <PERSON> <<EMAIL>>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.deletePreset=void 0,_ajax=(obj=_ajax)&&obj.__esModule?obj:{default:obj};_exports.deletePreset=presetId=>{const request={methodname:"tool_admin_presets_delete_preset",args:{id:presetId}};return _ajax.default.call([request])[0]}}));

//# sourceMappingURL=repository.min.js.map