{"version": 3, "file": "repository.min.js", "sources": ["../src/repository.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module to handle presets AJAX requests\n *\n * @module     tool_admin_presets/repository\n * @copyright  2024 <PERSON> <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Ajax from 'core/ajax';\n\n/**\n * Delete given preset\n *\n * @param {Number} presetId\n * @return {Promise}\n */\nexport const deletePreset = presetId => {\n    const request = {\n        methodname: 'tool_admin_presets_delete_preset',\n        args: {id: presetId}\n    };\n\n    return Ajax.call([request])[0];\n};\n"], "names": ["presetId", "request", "methodname", "args", "id", "Ajax", "call"], "mappings": ";;;;;;;oKA+B4BA,iBAClBC,QAAU,CACZC,WAAY,mCACZC,KAAM,CAACC,GAAIJ,kBAGRK,cAAKC,KAAK,CAACL,UAAU"}