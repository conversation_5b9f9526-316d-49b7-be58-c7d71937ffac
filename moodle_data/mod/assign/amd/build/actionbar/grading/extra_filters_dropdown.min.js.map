{"version": 3, "file": "extra_filters_dropdown.min.js", "sources": ["../../../src/actionbar/grading/extra_filters_dropdown.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport {getDropdownDialog} from 'core/local/dropdown/dialog';\nimport {getUserPreference} from 'core_user/repository';\n\n/**\n * Module for the extra filters dropdown on the submissions page.\n *\n * @module     mod_assign/actionbar/grading/extra_filters_dropdown\n * @copyright  2024 <PERSON><PERSON><PERSON> <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n/** @constant {Object} The object containing the relevant selectors. */\nconst Selectors = {\n    extraFiltersDropdown: '.dropdown.extrafilters',\n    extraFiltersClose: 'a[data-action=\"close\"]',\n    workflowFilterElement: 'select[name=\"workflowfilter\"]',\n    markerFilterElement: 'select[name=\"markingallocationfilter\"]',\n    suspendedParticipantsFilterCheckbox: 'input[type=\"checkbox\"][name=\"suspendedparticipantsfilter\"]',\n    suspendedParticipantsFilterHidden: 'input[type=\"hidden\"][name=\"suspendedparticipantsfilter\"]'\n};\n\n/**\n * Register event listeners for the extra filters dropdown.\n *\n * @param {DropdownDialog} extraFiltersDropdown The dropdown dialog instance.\n */\nconst registerEventListeners = (extraFiltersDropdown) => {\n    // Click event listener to the extra filters dropdown element.\n    extraFiltersDropdown.getElement().addEventListener('click', e => {\n        // The target is the 'Close' button.\n        if (e.target.closest(Selectors.extraFiltersClose)) {\n            e.preventDefault();\n            extraFiltersDropdown.setVisible(false);\n        }\n    });\n\n    // Change event listener to the extra filters dropdown element.\n    extraFiltersDropdown.getElement().addEventListener('change', e => {\n        const suspendedParticipantsFilterCheckbox = e.target.closest(Selectors.suspendedParticipantsFilterCheckbox);\n        // The target is the 'Suspended participants' filter checkbox.\n        if (suspendedParticipantsFilterCheckbox) {\n            // The 'Suspended participants' filter uses a hidden input and a checkbox. The hidden input is used to\n            // submit '0' as a workaround when the checkbox is unchecked since unchecked checkboxes are not submitted\n            // with the form. Therefore, we need to enable or disable the hidden input based on the checkbox state.\n            const suspendedParticipantsFilterHidden = suspendedParticipantsFilterCheckbox.parentNode\n                .querySelector(Selectors.suspendedParticipantsFilterHidden);\n            suspendedParticipantsFilterHidden.disabled = suspendedParticipantsFilterCheckbox.checked;\n        }\n    });\n\n    // Event listener triggered upon hiding of the dropdown.\n    extraFiltersDropdown.getElement().addEventListener('hide.bs.dropdown', () => {\n        // Restore the filters to their stored preference values once the dropdown is closed.\n        restoreAppliedWorkflowFilter(extraFiltersDropdown);\n        restoreAppliedMarkerFilter(extraFiltersDropdown);\n        restoreAppliedSuspendedParticipantsFilter(extraFiltersDropdown);\n    });\n};\n\n/**\n * Restores the currently applied workflow filter to its stored preference value.\n *\n * @param {DropdownDialog} extraFiltersDropdown The dropdown dialog instance.\n */\nconst restoreAppliedWorkflowFilter = async(extraFiltersDropdown) => {\n    const appliedWorkflowFilter = await getUserPreference('assign_workflowfilter');\n    const workflowFilterSelect = extraFiltersDropdown.getElement().querySelector(Selectors.workflowFilterElement);\n    workflowFilterSelect.value = appliedWorkflowFilter;\n};\n\n/**\n * Restores the currently applied marker filter to its stored preference value.\n *\n * @param {DropdownDialog} extraFiltersDropdown The dropdown dialog instance.\n */\nconst restoreAppliedMarkerFilter = async(extraFiltersDropdown) => {\n    const markerFilterSelect = extraFiltersDropdown.getElement().querySelector(Selectors.markerFilterElement);\n    if (markerFilterSelect) {\n        const appliedMarkerFilter = await getUserPreference('assign_markerfilter');\n        markerFilterSelect.value = appliedMarkerFilter;\n    }\n};\n\n/**\n * Restores the currently suspended participants filter to its stored preference value.\n *\n * @param {DropdownDialog} extraFiltersDropdown The dropdown dialog instance.\n */\nconst restoreAppliedSuspendedParticipantsFilter = async(extraFiltersDropdown) => {\n    const suspendedParticipantsFilterCheckbox = extraFiltersDropdown.getElement()\n        .querySelector(Selectors.suspendedParticipantsFilterCheckbox);\n    if (suspendedParticipantsFilterCheckbox) {\n        const suspendedParticipantsFilterHidden = suspendedParticipantsFilterCheckbox.parentNode\n            .querySelector(Selectors.suspendedParticipantsFilterHidden);\n        const showOnlyActiveParticipants = await getUserPreference('grade_report_showonlyactiveenrol');\n        suspendedParticipantsFilterCheckbox.checked = !showOnlyActiveParticipants;\n        suspendedParticipantsFilterHidden.disabled = !showOnlyActiveParticipants;\n    }\n};\n\n/**\n * Initialize module.\n */\nexport const init = () => {\n    const extraFiltersDropdown = getDropdownDialog(Selectors.extraFiltersDropdown);\n    if (extraFiltersDropdown) {\n        registerEventListeners(extraFiltersDropdown);\n    }\n};\n"], "names": ["Selectors", "restoreAppliedWorkflowFilter", "async", "appliedWorkflowFilter", "extraFiltersDropdown", "getElement", "querySelector", "value", "restoreAppliedMarkerFilter", "markerFilterSelect", "appliedMarkerFilter", "restoreAppliedSuspendedParticipantsFilter", "suspendedParticipantsFilterCheckbox", "suspendedParticipantsFilterHidden", "parentNode", "showOnlyActiveParticipants", "checked", "disabled", "addEventListener", "e", "target", "closest", "preventDefault", "setVisible", "registerEventListeners"], "mappings": ";;;;;;;;MA2BMA,+BACoB,yBADpBA,4BAEiB,yBAFjBA,gCAGqB,gCAHrBA,8BAImB,yCAJnBA,8CAKmC,6DALnCA,4CAMiC,2DA8CjCC,6BAA+BC,MAAAA,6BAC3BC,4BAA8B,iCAAkB,yBACzBC,qBAAqBC,aAAaC,cAAcN,iCACxDO,MAAQJ,uBAQ3BK,2BAA6BN,MAAAA,6BACzBO,mBAAqBL,qBAAqBC,aAAaC,cAAcN,kCACvES,mBAAoB,OACdC,0BAA4B,iCAAkB,uBACpDD,mBAAmBF,MAAQG,sBAS7BC,0CAA4CT,MAAAA,6BACxCU,oCAAsCR,qBAAqBC,aAC5DC,cAAcN,kDACfY,oCAAqC,OAC/BC,kCAAoCD,oCAAoCE,WACzER,cAAcN,6CACbe,iCAAmC,iCAAkB,oCAC3DH,oCAAoCI,SAAWD,2BAC/CF,kCAAkCI,UAAYF,2CAOlC,WACVX,sBAAuB,6BAAkBJ,gCAC3CI,sBA/EwBA,CAAAA,uBAE5BA,qBAAqBC,aAAaa,iBAAiB,SAASC,IAEpDA,EAAEC,OAAOC,QAAQrB,+BACjBmB,EAAEG,iBACFlB,qBAAqBmB,YAAW,OAKxCnB,qBAAqBC,aAAaa,iBAAiB,UAAUC,UACnDP,oCAAsCO,EAAEC,OAAOC,QAAQrB,+CAEzDY,sCAI0CA,oCAAoCE,WACzER,cAAcN,6CACeiB,SAAWL,oCAAoCI,YAKzFZ,qBAAqBC,aAAaa,iBAAiB,oBAAoB,KAEnEjB,6BAA6BG,sBAC7BI,2BAA2BJ,sBAC3BO,0CAA0CP,0BAmD1CoB,CAAuBpB"}