define("mod_assign/bulkactions/grading/bulk_actions",["exports","core/bulkactions/bulk_actions","./general_action","./delete","./extend","./message","./setmarkingallocation","./setmarkingworkflowstate","core/templates","core/str"],(function(_exports,_bulk_actions,_general_action,_delete,_extend2,_message2,_setmarkingallocation,_setmarkingworkflowstate,_templates,_str){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _classPrivateFieldInitSpec(obj,privateMap,value){!function(obj,privateCollection){if(privateCollection.has(obj))throw new TypeError("Cannot initialize the same private elements twice on an object")}(obj,privateMap),privateMap.set(obj,value)}function _classPrivateFieldGet(receiver,privateMap){return function(receiver,descriptor){if(descriptor.get)return descriptor.get.call(receiver);return descriptor.value}(receiver,_classExtractFieldDescriptor(receiver,privateMap,"get"))}function _classPrivateFieldSet(receiver,privateMap,value){return function(receiver,descriptor,value){if(descriptor.set)descriptor.set.call(receiver,value);else{if(!descriptor.writable)throw new TypeError("attempted to set read only private field");descriptor.value=value}}(receiver,_classExtractFieldDescriptor(receiver,privateMap,"set"),value),value}function _classExtractFieldDescriptor(receiver,privateMap,action){if(!privateMap.has(receiver))throw new TypeError("attempted to "+action+" private field on non-instance");return privateMap.get(receiver)}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_bulk_actions=_interopRequireDefault(_bulk_actions),_general_action=_interopRequireDefault(_general_action),_delete=_interopRequireDefault(_delete),_extend2=_interopRequireDefault(_extend2),_message2=_interopRequireDefault(_message2),_setmarkingallocation=_interopRequireDefault(_setmarkingallocation),_setmarkingworkflowstate=_interopRequireDefault(_setmarkingworkflowstate),_templates=_interopRequireDefault(_templates);const Selectors_selectBulkItemCheckbox='input[type="checkbox"][name="selectedusers"]',Selectors_selectBulkItemTrigger='input[type="checkbox"][name="selectedusers"], input[type="checkbox"][name="selectall"]';var _cmid=new WeakMap,_extend=new WeakMap,_grantAttempt=new WeakMap,_markingAllocation=new WeakMap,_message=new WeakMap,_pluginOperations=new WeakMap,_removeSubmission=new WeakMap,_sesskey=new WeakMap,_submissionDrafts=new WeakMap,_workflowState=new WeakMap;class _default extends _bulk_actions.default{static init(options){return new this(options)}constructor(_ref){let{cmid:cmid,message:message,submissiondrafts:submissiondrafts,removesubmission:removesubmission,extend:extend,grantattempt:grantattempt,workflowstate:workflowstate,markingallocation:markingallocation,pluginoperations:pluginoperations,sesskey:sesskey}=_ref;super(),_classPrivateFieldInitSpec(this,_cmid,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,_extend,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,_grantAttempt,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,_markingAllocation,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,_message,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,_pluginOperations,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,_removeSubmission,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,_sesskey,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,_submissionDrafts,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,_workflowState,{writable:!0,value:void 0}),_classPrivateFieldSet(this,_cmid,cmid),_classPrivateFieldSet(this,_message,message),_classPrivateFieldSet(this,_submissionDrafts,submissiondrafts),_classPrivateFieldSet(this,_removeSubmission,removesubmission),_classPrivateFieldSet(this,_extend,extend),_classPrivateFieldSet(this,_grantAttempt,grantattempt),_classPrivateFieldSet(this,_workflowState,workflowstate),_classPrivateFieldSet(this,_markingAllocation,markingallocation),_classPrivateFieldSet(this,_sesskey,sesskey),_classPrivateFieldSet(this,_pluginOperations,pluginoperations)}getBulkActions(){const actions=[new _general_action.default(_classPrivateFieldGet(this,_cmid),_classPrivateFieldGet(this,_sesskey),"lock",(0,_str.getString)("batchoperationlock","mod_assign"),_templates.default.renderPix("i/lock","core"),(0,_str.getString)("locksubmissions","mod_assign"),(0,_str.getString)("batchoperationconfirmlock","mod_assign"),(0,_str.getString)("batchoperationlock","mod_assign")),new _general_action.default(_classPrivateFieldGet(this,_cmid),_classPrivateFieldGet(this,_sesskey),"unlock",(0,_str.getString)("batchoperationunlock","mod_assign"),_templates.default.renderPix("i/unlock","core"),(0,_str.getString)("unlocksubmissions","mod_assign"),(0,_str.getString)("batchoperationconfirmunlock","mod_assign"),(0,_str.getString)("batchoperationunlock","mod_assign")),new _general_action.default(_classPrivateFieldGet(this,_cmid),_classPrivateFieldGet(this,_sesskey),"downloadselected",(0,_str.getString)("batchoperationdownloadselected","mod_assign"),_templates.default.renderPix("t/download","core"),(0,_str.getString)("downloadselectedsubmissions","mod_assign"),(0,_str.getString)("batchoperationconfirmdownloadselected","mod_assign"),(0,_str.getString)("batchoperationdownloadselected","mod_assign"))];_classPrivateFieldGet(this,_removeSubmission)&&actions.push(new _delete.default(_classPrivateFieldGet(this,_cmid),_classPrivateFieldGet(this,_sesskey))),_classPrivateFieldGet(this,_extend)&&actions.push(new _extend2.default(_classPrivateFieldGet(this,_cmid),_classPrivateFieldGet(this,_sesskey))),_classPrivateFieldGet(this,_grantAttempt)&&actions.push(new _general_action.default(_classPrivateFieldGet(this,_cmid),_classPrivateFieldGet(this,_sesskey),"addattempt",(0,_str.getString)("batchoperationaddattempt","mod_assign"),_templates.default.renderPix("t/add","core"),(0,_str.getString)("addattempt","mod_assign"),(0,_str.getString)("batchoperationconfirmaddattempt","mod_assign"),(0,_str.getString)("batchoperationaddattempt","mod_assign"))),_classPrivateFieldGet(this,_workflowState)&&actions.push(new _setmarkingworkflowstate.default(_classPrivateFieldGet(this,_cmid),_classPrivateFieldGet(this,_sesskey))),_classPrivateFieldGet(this,_markingAllocation)&&actions.push(new _setmarkingallocation.default(_classPrivateFieldGet(this,_cmid),_classPrivateFieldGet(this,_sesskey))),_classPrivateFieldGet(this,_submissionDrafts)&&actions.push(new _general_action.default(_classPrivateFieldGet(this,_cmid),_classPrivateFieldGet(this,_sesskey),"reverttodraft",(0,_str.getString)("batchoperationreverttodraft","mod_assign"),_templates.default.renderPix("e/undo","core"),(0,_str.getString)("reverttodraft","mod_assign"),(0,_str.getString)("batchoperationconfirmreverttodraft","mod_assign"),(0,_str.getString)("batchoperationreverttodraft","mod_assign"))),_classPrivateFieldGet(this,_message)&&actions.push(new _message2.default);for(const operation of _classPrivateFieldGet(this,_pluginOperations))actions.push(new _general_action.default(_classPrivateFieldGet(this,_cmid),_classPrivateFieldGet(this,_sesskey),operation.key,operation.label,operation.icon,operation.confirmationtitle,operation.confirmationquestion));return actions}getSelectedItems(){return document.querySelectorAll("".concat(Selectors_selectBulkItemCheckbox,":checked"))}registerItemSelectChangeEvent(eventHandler){document.querySelectorAll(Selectors_selectBulkItemTrigger).forEach((checkbox=>{checkbox.addEventListener("change",eventHandler.bind(this))}))}deselectItem(selectedItem){selectedItem.checked=!1,selectedItem.closest("tr").classList.replace("selectedrow","unselectedrow")}}return _exports.default=_default,_exports.default}));

//# sourceMappingURL=bulk_actions.min.js.map