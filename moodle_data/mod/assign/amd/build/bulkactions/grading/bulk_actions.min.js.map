{"version": 3, "file": "bulk_actions.min.js", "sources": ["../../../src/bulkactions/grading/bulk_actions.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Class for defining the bulk actions area in the assignment grading page.\n *\n * @module     mod_assign/bulkactions/grading/bulk_actions\n * @copyright  2024 Shamim Rezaie <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport BulkActions from 'core/bulkactions/bulk_actions';\nimport GeneralAction from './general_action';\nimport DeleteAction from './delete';\nimport ExtendAction from './extend';\nimport MessageAction from './message';\nimport SetMarkingAllocationAction from './setmarkingallocation';\nimport SetMarkingWorkflowStateAction from './setmarkingworkflowstate';\nimport Templates from 'core/templates';\nimport {getString} from 'core/str';\n\nconst Selectors = {\n    selectBulkItemCheckbox: 'input[type=\"checkbox\"][name=\"selectedusers\"]',\n    selectBulkItemTrigger: 'input[type=\"checkbox\"][name=\"selectedusers\"], input[type=\"checkbox\"][name=\"selectall\"]',\n};\n\nexport default class extends BulkActions {\n    /** @type {number} The course module ID. */\n    #cmid;\n\n    /** @type {boolean} Whether to show the extend action. */\n    #extend;\n\n    /** @type {boolean} Whether to show the grant extension action. */\n    #grantAttempt;\n\n    /** @type {boolean} Whether to show the set marking allocation action. */\n    #markingAllocation;\n\n    /** @type {string} Whether to show the message action. */\n    #message;\n\n    /** @type {Array} The list of plugin operations. */\n    #pluginOperations;\n\n    /** @type {boolean} Whether to show the remove submission action. */\n    #removeSubmission;\n\n    /** @type {string} The session key. */\n    #sesskey;\n\n    /** @type {boolean} Whether to show the revert to draft action. */\n    #submissionDrafts;\n\n    /** @type {boolean} Whether to show the set workflow state action. */\n    #workflowState;\n\n    /**\n     * Returns the instance of the class.\n     *\n     * @param {Object} options - The options object.\n     * @param {number} options.cmid - The course module ID.\n     * @param {string} options.message - Whether to show the message action.\n     * @param {boolean} options.submissiondrafts - Whether to show the revert to draft action.\n     * @param {boolean} options.removesubmission - Whether to show the remove submission action.\n     * @param {boolean} options.extend - Whether to show the grant extension action.\n     * @param {boolean} options.grantattempt - Whether to show the grant attempt action.\n     * @param {boolean} options.workflowstate - Whether to show the set workflow state action.\n     * @param {boolean} options.markingallocation - Whether to show the set marking allocation action.\n     * @param {Array} options.pluginoperations - The list of plugin operations.\n     * @param {string} options.sesskey - The session key.\n     * @returns {this} An instance of the anonymous class extending BulkActions.\n     */\n    static init(options) {\n        return new this(options);\n    }\n\n    /**\n     * The class constructor\n     *\n     * @param {Object} options - The options object.\n     * @param {number} options.cmid - The course module ID.\n     * @param {string} options.message - Whether to show the message action.\n     * @param {boolean} options.submissiondrafts - Whether to show the revert to draft action.\n     * @param {boolean} options.removesubmission - Whether to show the remove submission action.\n     * @param {boolean} options.extend - Whether to show the grant extension action.\n     * @param {boolean} options.grantattempt - Whether to show the grant attempt action.\n     * @param {boolean} options.workflowstate - Whether to show the set workflow state action.\n     * @param {boolean} options.markingallocation - Whether to show the set marking allocation action.\n     * @param {Array} options.pluginoperations - The list of plugin operations.\n     * @param {string} options.sesskey - The session key.\n     */\n    constructor({\n        cmid, message, submissiondrafts, removesubmission, extend,\n        grantattempt, workflowstate, markingallocation, pluginoperations, sesskey\n    }) {\n        super();\n        this.#cmid = cmid;\n        this.#message = message;\n        this.#submissionDrafts = submissiondrafts;\n        this.#removeSubmission = removesubmission;\n        this.#extend = extend;\n        this.#grantAttempt = grantattempt;\n        this.#workflowState = workflowstate;\n        this.#markingAllocation = markingallocation;\n        this.#sesskey = sesskey;\n        this.#pluginOperations = pluginoperations;\n    }\n\n    getBulkActions() {\n        const actions = [\n            new GeneralAction(\n                this.#cmid,\n                this.#sesskey,\n                'lock',\n                getString('batchoperationlock', 'mod_assign'),\n                Templates.renderPix('i/lock', 'core'),\n                getString('locksubmissions', 'mod_assign'),\n                getString('batchoperationconfirmlock', 'mod_assign'),\n                getString('batchoperationlock', 'mod_assign'),\n            ),\n            new GeneralAction(\n                this.#cmid,\n                this.#sesskey,\n                'unlock',\n                getString('batchoperationunlock', 'mod_assign'),\n                Templates.renderPix('i/unlock', 'core'),\n                getString('unlocksubmissions', 'mod_assign'),\n                getString('batchoperationconfirmunlock', 'mod_assign'),\n                getString('batchoperationunlock', 'mod_assign'),\n            ),\n            new GeneralAction(\n                this.#cmid,\n                this.#sesskey,\n                'downloadselected',\n                getString('batchoperationdownloadselected', 'mod_assign'),\n                Templates.renderPix('t/download', 'core'),\n                getString('downloadselectedsubmissions', 'mod_assign'),\n                getString('batchoperationconfirmdownloadselected', 'mod_assign'),\n                getString('batchoperationdownloadselected', 'mod_assign'),\n            ),\n        ];\n\n        if (this.#removeSubmission) {\n            actions.push(new DeleteAction(this.#cmid, this.#sesskey));\n        }\n\n        if (this.#extend) {\n            actions.push(new ExtendAction(this.#cmid, this.#sesskey));\n        }\n\n        if (this.#grantAttempt) {\n            actions.push(\n                new GeneralAction(\n                    this.#cmid,\n                    this.#sesskey,\n                    'addattempt',\n                    getString('batchoperationaddattempt', 'mod_assign'),\n                    Templates.renderPix('t/add', 'core'),\n                    getString('addattempt', 'mod_assign'),\n                    getString('batchoperationconfirmaddattempt', 'mod_assign'),\n                    getString('batchoperationaddattempt', 'mod_assign'),\n                )\n            );\n        }\n\n        if (this.#workflowState) {\n            actions.push(new SetMarkingWorkflowStateAction(this.#cmid, this.#sesskey));\n        }\n\n        if (this.#markingAllocation) {\n            actions.push(new SetMarkingAllocationAction(this.#cmid, this.#sesskey));\n        }\n\n        if (this.#submissionDrafts) {\n            actions.push(\n                new GeneralAction(\n                    this.#cmid,\n                    this.#sesskey,\n                    'reverttodraft',\n                    getString('batchoperationreverttodraft', 'mod_assign'),\n                    Templates.renderPix('e/undo', 'core'),\n                    getString('reverttodraft', 'mod_assign'),\n                    getString('batchoperationconfirmreverttodraft', 'mod_assign'),\n                    getString('batchoperationreverttodraft', 'mod_assign'),\n                )\n            );\n        }\n\n        if (this.#message) {\n            actions.push(new MessageAction());\n        }\n\n        for (const operation of this.#pluginOperations) {\n            actions.push(\n                new GeneralAction(\n                    this.#cmid,\n                    this.#sesskey,\n                    operation.key,\n                    operation.label,\n                    operation.icon,\n                    operation.confirmationtitle,\n                    operation.confirmationquestion,\n                )\n            );\n        }\n\n        return actions;\n    }\n\n    getSelectedItems() {\n        return document.querySelectorAll(`${Selectors.selectBulkItemCheckbox}:checked`);\n    }\n\n    registerItemSelectChangeEvent(eventHandler) {\n        const itemSelectCheckboxes = document.querySelectorAll(Selectors.selectBulkItemTrigger);\n        itemSelectCheckboxes.forEach((checkbox) => {\n            checkbox.addEventListener('change', eventHandler.bind(this));\n        });\n    }\n\n    deselectItem(selectedItem) {\n        selectedItem.checked = false;\n        selectedItem.closest('tr').classList.replace('selectedrow', 'unselectedrow');\n    }\n}\n"], "names": ["Selectors", "BulkActions", "options", "this", "constructor", "cmid", "message", "submissiondrafts", "removesubmission", "extend", "grantattempt", "workflowstate", "markingallocation", "pluginoperations", "sesskey", "getBulkActions", "actions", "GeneralAction", "Templates", "renderPix", "push", "DeleteAction", "ExtendAction", "SetMarkingWorkflowStateAction", "SetMarkingAllocationAction", "MessageAction", "operation", "key", "label", "icon", "confirmationtitle", "confirmationquestion", "getSelectedItems", "document", "querySelectorAll", "registerItemSelectChangeEvent", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "checkbox", "addEventListener", "bind", "deselectItem", "selectedItem", "checked", "closest", "classList", "replace"], "mappings": "i9DAiCMA,iCACsB,+CADtBA,gCAEqB,kXAGEC,kCA+CbC,gBACD,IAAIC,KAAKD,SAkBpBE,sBAAYC,KACRA,KADQC,QACFA,QADEC,iBACOA,iBADPC,iBACyBA,iBADzBC,OAC2CA,OAD3CC,aAERA,aAFQC,cAEMA,cAFNC,kBAEqBA,kBAFrBC,iBAEwCA,iBAFxCC,QAE0DA,qxBAGrDT,0CACGC,sDACSC,+DACAC,qDACVC,iDACMC,wDACCC,6DACIC,uDACVE,sDACSD,kBAG7BE,uBACUC,QAAU,CACZ,IAAIC,8CACAd,kCACAA,eACA,QACA,kBAAU,qBAAsB,cAChCe,mBAAUC,UAAU,SAAU,SAC9B,kBAAU,kBAAmB,eAC7B,kBAAU,4BAA6B,eACvC,kBAAU,qBAAsB,eAEpC,IAAIF,8CACAd,kCACAA,eACA,UACA,kBAAU,uBAAwB,cAClCe,mBAAUC,UAAU,WAAY,SAChC,kBAAU,oBAAqB,eAC/B,kBAAU,8BAA+B,eACzC,kBAAU,uBAAwB,eAEtC,IAAIF,8CACAd,kCACAA,eACA,oBACA,kBAAU,iCAAkC,cAC5Ce,mBAAUC,UAAU,aAAc,SAClC,kBAAU,8BAA+B,eACzC,kBAAU,wCAAyC,eACnD,kBAAU,iCAAkC,sCAIhDhB,yBACAa,QAAQI,KAAK,IAAIC,sCAAalB,kCAAYA,uCAG1CA,eACAa,QAAQI,KAAK,IAAIE,uCAAanB,kCAAYA,uCAG1CA,qBACAa,QAAQI,KACJ,IAAIH,8CACAd,kCACAA,eACA,cACA,kBAAU,2BAA4B,cACtCe,mBAAUC,UAAU,QAAS,SAC7B,kBAAU,aAAc,eACxB,kBAAU,kCAAmC,eAC7C,kBAAU,2BAA4B,sCAK9ChB,sBACAa,QAAQI,KAAK,IAAIG,uDAA8BpB,kCAAYA,uCAG3DA,0BACAa,QAAQI,KAAK,IAAII,oDAA2BrB,kCAAYA,uCAGxDA,yBACAa,QAAQI,KACJ,IAAIH,8CACAd,kCACAA,eACA,iBACA,kBAAU,8BAA+B,cACzCe,mBAAUC,UAAU,SAAU,SAC9B,kBAAU,gBAAiB,eAC3B,kBAAU,qCAAsC,eAChD,kBAAU,8BAA+B,sCAKjDhB,gBACAa,QAAQI,KAAK,IAAIK,uBAGhB,MAAMC,mCAAavB,wBACpBa,QAAQI,KACJ,IAAIH,8CACAd,kCACAA,eACAuB,UAAUC,IACVD,UAAUE,MACVF,UAAUG,KACVH,UAAUI,kBACVJ,UAAUK,8BAKff,QAGXgB,0BACWC,SAASC,2BAAoBlC,8CAGxCmC,8BAA8BC,cACGH,SAASC,iBAAiBlC,iCAClCqC,SAASC,WAC1BA,SAASC,iBAAiB,SAAUH,aAAaI,KAAKrC,UAI9DsC,aAAaC,cACTA,aAAaC,SAAU,EACvBD,aAAaE,QAAQ,MAAMC,UAAUC,QAAQ,cAAe"}