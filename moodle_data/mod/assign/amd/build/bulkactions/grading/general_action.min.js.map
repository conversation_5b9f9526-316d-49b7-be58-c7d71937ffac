{"version": 3, "file": "general_action.min.js", "sources": ["../../../src/bulkactions/grading/general_action.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Class that defines the bulk action for general actions in the assignment grading page.\n *\n * @module     mod_assign/bulkactions/grading/general_action\n * @copyright  2024 Shamim Rezaie <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport BulkAction from 'core/bulkactions/bulk_action';\nimport Templates from 'core/templates';\nimport SaveCancelModal from 'core/modal_save_cancel';\nimport ModalEvents from 'core/modal_events';\n\nconst Selectors = {\n    selectBulkItemCheckbox: 'input[type=\"checkbox\"][name=\"selectedusers\"]:checked',\n};\n\nexport default class extends BulkAction {\n\n    /** @type {string} The action key. */\n    actionKey;\n\n    /** @type {number} The course module ID. */\n    #cmid;\n\n    /** @type {Promise<string>} The action button's icon. */\n    #buttonIcon;\n\n    /** @type {Promise<string>} The action button's label. */\n    #buttonLabel;\n\n    /** @type {Promise<string>} Title of the confirmation dialog. */\n    #confirmationTitle;\n\n    /** @type {Promise<string>} Question of the confirmation dialog. */\n    #confirmationQuestion;\n\n    /** @type {Promise<string>} Text for the confirmation yes button. */\n    #confirmationYes;\n\n    /** @type {string} The session key. */\n    #sesskey;\n\n    /**\n     * The class constructor.\n     *\n     * @param {int} cmid The course module ID.\n     * @param {string} sesskey The session key.\n     * @param {string} actionKey The action key.\n     * @param {Promise<string>} buttonLabel The action button's label.\n     * @param {Promise<string>} buttonIcon The action button's icon.\n     * @param {Promise<string>} confirmationTitle Title of the confirmation dialog.\n     * @param {Promise<string>} confirmationQuestion Question of the confirmation dialog.\n     * @param {Promise<string>} confirmationYes Text for the confirmation yes button.\n     */\n    constructor(cmid, sesskey, actionKey, buttonLabel, buttonIcon, confirmationTitle, confirmationQuestion, confirmationYes) {\n        super();\n        this.#cmid = cmid;\n        this.#sesskey = sesskey;\n        this.actionKey = actionKey;\n        this.#buttonLabel = buttonLabel;\n        this.#buttonIcon = buttonIcon;\n        this.#confirmationTitle = confirmationTitle;\n        this.#confirmationQuestion = confirmationQuestion;\n        this.#confirmationYes = confirmationYes;\n    }\n\n    getBulkActionTriggerSelector() {\n        return `[data-type=\"bulkactions\"] [data-action=\"${this.actionKey}\"]`;\n    }\n\n    async triggerBulkAction() {\n        const selectedUsers = [...document.querySelectorAll(Selectors.selectBulkItemCheckbox)].map(checkbox => checkbox.value);\n\n        const modal = await SaveCancelModal.create({\n            title: await this.#confirmationTitle,\n            buttons: {\n                save: await this.#confirmationYes,\n            },\n            body: Templates.render('mod_assign/bulkactions/grading/bulk_action_modal_body', {\n                text: await this.#confirmationQuestion,\n                operation: this.actionKey,\n                cmid: this.#cmid,\n                selectedusers: selectedUsers.join(','),\n                sesskey: this.#sesskey\n            }),\n            show: true,\n            removeOnClose: true,\n        });\n\n        // Handle save event.\n        modal.getRoot().on(ModalEvents.save, (e) => {\n            e.preventDefault();\n            modal.getRoot().find('form').submit();\n        });\n    }\n\n    async renderBulkActionTrigger(showInDropdown, index) {\n        return Templates.render('mod_assign/bulkactions/grading/bulk_general_action_trigger', {\n            action: this.actionKey,\n            title: await this.#buttonLabel,\n            icon: await this.#buttonIcon,\n            showindropdown: showInDropdown,\n            isfirst: index === 0,\n        });\n    }\n}\n"], "names": ["Selectors", "BulkAction", "constructor", "cmid", "sesskey", "action<PERSON>ey", "buttonLabel", "buttonIcon", "confirmationTitle", "confirmationQuestion", "confirmationYes", "getBulkActionTriggerSelector", "this", "selectedUsers", "document", "querySelectorAll", "map", "checkbox", "value", "modal", "SaveCancelModal", "create", "title", "buttons", "save", "body", "Templates", "render", "text", "operation", "selectedusers", "join", "show", "removeOnClose", "getRoot", "on", "ModalEvents", "e", "preventDefault", "find", "submit", "showInDropdown", "index", "action", "icon", "showindropdown", "isfirst"], "mappings": "unDA4BMA,iCACsB,wQAGCC,qBAsCzBC,YAAYC,KAAMC,QAASC,UAAWC,YAAaC,WAAYC,kBAAmBC,qBAAsBC,muBAEvFP,0CACGC,cACXC,UAAYA,kDACGC,oDACDC,0DACOC,oEACGC,kEACLC,iBAG5BC,uFACsDC,KAAKP,gDAIjDQ,cAAgB,IAAIC,SAASC,iBAAiBf,mCAAmCgB,KAAIC,UAAYA,SAASC,QAE1GC,YAAcC,2BAAgBC,OAAO,CACvCC,kCAAaV,yBACbW,QAAS,CACLC,iCAAYZ,wBAEhBa,KAAMC,mBAAUC,OAAO,wDAAyD,CAC5EC,iCAAYhB,4BACZiB,UAAWjB,KAAKP,UAChBF,2BAAMS,YACNkB,cAAejB,cAAckB,KAAK,KAClC3B,8BAASQ,iBAEboB,MAAM,EACNC,eAAe,IAInBd,MAAMe,UAAUC,GAAGC,sBAAYZ,MAAOa,IAClCA,EAAEC,iBACFnB,MAAMe,UAAUK,KAAK,QAAQC,0CAIPC,eAAgBC,cACnChB,mBAAUC,OAAO,6DAA8D,CAClFgB,OAAQ/B,KAAKP,UACbiB,kCAAaV,mBACbgC,iCAAYhC,kBACZiC,eAAgBJ,eAChBK,QAAmB,IAAVJ"}