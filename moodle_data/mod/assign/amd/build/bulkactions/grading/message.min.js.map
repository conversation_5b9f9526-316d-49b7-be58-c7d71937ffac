{"version": 3, "file": "message.min.js", "sources": ["../../../src/bulkactions/grading/message.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Bulk action for messaging users in the assignment grading page.\n *\n * @module     mod_assign/bulkactions/grading/message\n * @copyright  2024 Shamim Rezaie <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport BulkAction from 'core/bulkactions/bulk_action';\nimport Templates from 'core/templates';\nimport {showModal as showMessageModal} from 'core_message/message_send_bulk';\n\nconst Selectors = {\n    selectBulkItemCheckbox: 'input[type=\"checkbox\"][name=\"selectedusers\"]:checked',\n};\n\nexport default class extends BulkAction {\n    getBulkActionTriggerSelector() {\n        return '[data-type=\"bulkactions\"] [data-action=\"message\"]';\n    }\n\n    triggerBulkAction() {\n        const selectedUsers = [...document.querySelectorAll(Selectors.selectBulkItemCheckbox)].map(checkbox => checkbox.value);\n        showMessageModal(selectedUsers);\n    }\n\n    async renderBulkActionTrigger(showInDropdown, index) {\n        return Templates.render('mod_assign/bulkactions/grading/bulk_message_trigger', {\n            showindropdown: showInDropdown,\n            isfirst: index === 0,\n        });\n    }\n}\n"], "names": ["Selectors", "BulkAction", "getBulkActionTriggerSelector", "triggerBulkAction", "selectedUsers", "document", "querySelectorAll", "map", "checkbox", "value", "showInDropdown", "index", "Templates", "render", "showindropdown", "isfirst"], "mappings": ";;;;;;;2LA2BMA,iCACsB,8EAGCC,qBACzBC,qCACW,oDAGXC,0BACUC,cAAgB,IAAIC,SAASC,iBAAiBN,mCAAmCO,KAAIC,UAAYA,SAASC,yCAC/FL,6CAGSM,eAAgBC,cACnCC,mBAAUC,OAAO,sDAAuD,CAC3EC,eAAgBJ,eAChBK,QAAmB,IAAVJ"}