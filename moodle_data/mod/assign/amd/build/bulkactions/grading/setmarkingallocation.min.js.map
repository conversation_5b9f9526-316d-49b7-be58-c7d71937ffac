{"version": 3, "file": "setmarkingallocation.min.js", "sources": ["../../../src/bulkactions/grading/setmarkingallocation.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Bulk action for allocating markers in the assignment grading page.\n *\n * @module     mod_assign/bulkactions/grading/setmarkingallocation\n * @copyright  2024 Shamim <PERSON>zaie <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport BulkAction from 'core/bulkactions/bulk_action';\nimport Templates from 'core/templates';\nimport {getString} from 'core/str';\nimport SaveCancelModal from 'core/modal_save_cancel';\nimport ModalEvents from 'core/modal_events';\n\nconst Selectors = {\n    selectBulkItemCheckbox: 'input[type=\"checkbox\"][name=\"selectedusers\"]:checked',\n};\n\nexport default class extends BulkAction {\n    /** @type {number} The course module ID. */\n    #cmid;\n\n    /** @type {string} The session key. */\n    #sesskey;\n\n    /**\n     * The class constructor.\n     *\n     * @param {number} cmid The course module ID.\n     * @param {string} sesskey The session key.\n     */\n    constructor(cmid, sesskey) {\n        super();\n        this.#cmid = cmid;\n        this.#sesskey = sesskey;\n    }\n\n    getBulkActionTriggerSelector() {\n        return '[data-type=\"bulkactions\"] [data-action=\"setmarkingallocation\"]';\n    }\n\n    async triggerBulkAction() {\n        const selectedUsers = [...document.querySelectorAll(Selectors.selectBulkItemCheckbox)].map(checkbox => checkbox.value);\n\n        const modal = await SaveCancelModal.create({\n            title: await getString('setmarkingallocation', 'mod_assign'),\n            buttons: {\n                save: await getString('batchoperationsetmarkingallocation', 'mod_assign'),\n            },\n            body: Templates.render('mod_assign/bulkactions/grading/bulk_action_modal_body', {\n                text: await getString('batchoperationconfirmsetmarkingallocation', 'mod_assign'),\n                operation: 'setmarkingallocation',\n                cmid: this.#cmid,\n                selectedusers: selectedUsers.join(','),\n                sesskey: this.#sesskey\n            }),\n            show: true,\n            removeOnClose: true,\n        });\n\n        // Handle save event.\n        modal.getRoot().on(ModalEvents.save, (e) => {\n            e.preventDefault();\n            modal.getRoot().find('form').submit();\n        });\n    }\n\n    async renderBulkActionTrigger(showInDropdown, index) {\n        return Templates.render('mod_assign/bulkactions/grading/bulk_setmarkingallocation_trigger', {\n            showindropdown: showInDropdown,\n            isfirst: index === 0,\n        });\n    }\n}\n"], "names": ["Selectors", "BulkAction", "constructor", "cmid", "sesskey", "getBulkActionTriggerSelector", "selectedUsers", "document", "querySelectorAll", "map", "checkbox", "value", "modal", "SaveCancelModal", "create", "title", "buttons", "save", "body", "Templates", "render", "text", "operation", "this", "selectedusers", "join", "show", "removeOnClose", "getRoot", "on", "ModalEvents", "e", "preventDefault", "find", "submit", "showInDropdown", "index", "showindropdown", "isfirst"], "mappings": "6oDA6BMA,iCACsB,yHAGCC,qBAazBC,YAAYC,KAAMC,yLAEDD,0CACGC,SAGpBC,qCACW,iGAIDC,cAAgB,IAAIC,SAASC,iBAAiBR,mCAAmCS,KAAIC,UAAYA,SAASC,QAE1GC,YAAcC,2BAAgBC,OAAO,CACvCC,YAAa,kBAAU,uBAAwB,cAC/CC,QAAS,CACLC,WAAY,kBAAU,qCAAsC,eAEhEC,KAAMC,mBAAUC,OAAO,wDAAyD,CAC5EC,WAAY,kBAAU,4CAA6C,cACnEC,UAAW,uBACXnB,2BAAMoB,YACNC,cAAelB,cAAcmB,KAAK,KAClCrB,8BAASmB,iBAEbG,MAAM,EACNC,eAAe,IAInBf,MAAMgB,UAAUC,GAAGC,sBAAYb,MAAOc,IAClCA,EAAEC,iBACFpB,MAAMgB,UAAUK,KAAK,QAAQC,0CAIPC,eAAgBC,cACnCjB,mBAAUC,OAAO,mEAAoE,CACxFiB,eAAgBF,eAChBG,QAAmB,IAAVF"}