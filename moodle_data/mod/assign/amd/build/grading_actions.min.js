/**
 * Javascript controller for the "Actions" panel at the bottom of the page.
 *
 * @module     mod_assign/grading_actions
 * @copyright  2016 Damyon Wiese <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 * @since      3.1
 */
define("mod_assign/grading_actions",["jquery","mod_assign/grading_events"],(function($,GradingEvents){var GradingActions=function(selector){this._regionSelector=selector,this._region=$(selector),this.registerEventListeners()};return GradingActions.prototype._regionSelector=null,GradingActions.prototype._lastUserId=0,GradingActions.prototype._region=null,GradingActions.prototype._showActionsForm=function(event,userid){var form=this._region.find("[data-region=grading-actions-form]");userid!=this._lastUserId&&userid>0&&(this._lastUserId=userid),userid>0?form.removeClass("hide"):form.addClass("hide")},GradingActions.prototype._trigger=function(action){$(document).trigger(action)},GradingActions.prototype.getReviewPanelElement=function(){return $('[data-region="review-panel"]')},GradingActions.prototype.hasReviewPanelElement=function(){return this.getReviewPanelElement().length>0},GradingActions.prototype.getCollapseGradePanelButton=function(){return $('[data-region="grade-actions"] .collapse-grade-panel')},GradingActions.prototype.getCollapseReviewPanelButton=function(){return $('[data-region="grade-actions"] .collapse-review-panel')},GradingActions.prototype.getExpandAllPanelsButton=function(){return $('[data-region="grade-actions"] .collapse-none')},GradingActions.prototype.resetLayoutButtons=function(){this.getCollapseGradePanelButton().removeClass("active"),this.getCollapseReviewPanelButton().removeClass("active"),this.getExpandAllPanelsButton().removeClass("active")},GradingActions.prototype.collapseReviewPanel=function(){$(document).trigger(GradingEvents.COLLAPSE_REVIEW_PANEL),$(document).trigger(GradingEvents.EXPAND_GRADE_PANEL),this.resetLayoutButtons(),this.getCollapseReviewPanelButton().addClass("active")},GradingActions.prototype.collapseGradePanel=function(){$(document).trigger(GradingEvents.COLLAPSE_GRADE_PANEL),$(document).trigger(GradingEvents.EXPAND_REVIEW_PANEL),this.resetLayoutButtons(),this.getCollapseGradePanelButton().addClass("active")},GradingActions.prototype.expandAllPanels=function(){$(document).trigger(GradingEvents.EXPAND_GRADE_PANEL),$(document).trigger(GradingEvents.EXPAND_REVIEW_PANEL),this.resetLayoutButtons(),this.getExpandAllPanelsButton().addClass("active")},GradingActions.prototype.registerEventListeners=function(){if(this.hasReviewPanelElement()){var collapseReviewPanelButton=this.getCollapseReviewPanelButton();collapseReviewPanelButton.click(function(e){this.collapseReviewPanel(),e.preventDefault()}.bind(this)),collapseReviewPanelButton.keydown(function(e){e.metaKey||e.shiftKey||e.altKey||e.ctrlKey||13!==e.keyCode&&32!==e.keyCode||(this.collapseReviewPanel(),e.preventDefault())}.bind(this));var collapseGradePanelButton=this.getCollapseGradePanelButton();collapseGradePanelButton.click(function(e){this.collapseGradePanel(),e.preventDefault()}.bind(this)),collapseGradePanelButton.keydown(function(e){e.metaKey||e.shiftKey||e.altKey||e.ctrlKey||13!==e.keyCode&&32!==e.keyCode||(this.collapseGradePanel(),e.preventDefault())}.bind(this));var expandAllPanelsButton=this.getExpandAllPanelsButton();expandAllPanelsButton.click(function(e){this.expandAllPanels(),e.preventDefault()}.bind(this)),expandAllPanelsButton.keydown(function(e){e.metaKey||e.shiftKey||e.altKey||e.ctrlKey||13!==e.keyCode&&32!==e.keyCode||(this.expandAllPanels(),e.preventDefault())}.bind(this))}$(document).on("user-changed",this._showActionsForm.bind(this)),this._region.find('[name="savechanges"]').on("click",this._trigger.bind(this,"save-changes")),this._region.find('[name="saveandshownext"]').on("click",this._trigger.bind(this,"save-and-show-next")),this._region.find('[name="resetbutton"]').on("click",this._trigger.bind(this,"reset")),this._region.find("form").on("submit",(function(e){e.preventDefault()}))},GradingActions}));

//# sourceMappingURL=grading_actions.min.js.map