{"version": 3, "file": "grading_actions.min.js", "sources": ["../src/grading_actions.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Javascript controller for the \"Actions\" panel at the bottom of the page.\n *\n * @module     mod_assign/grading_actions\n * @copyright  2016 Damyon Wiese <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n * @since      3.1\n */\ndefine(['jquery', 'mod_assign/grading_events'], function($, GradingEvents) {\n\n    /**\n     * GradingActions class.\n     *\n     * @class mod_assign/grading_actions\n     * @param {String} selector The selector for the page region containing the actions panel.\n     */\n    var GradingActions = function(selector) {\n        this._regionSelector = selector;\n        this._region = $(selector);\n\n        this.registerEventListeners();\n    };\n\n    /** @property {String} Selector for the page region containing the user navigation. */\n    GradingActions.prototype._regionSelector = null;\n\n    /** @property {Integer} Remember the last user id to prevent unnessecary reloads. */\n    GradingActions.prototype._lastUserId = 0;\n\n    /** @property {JQuery} JQuery node for the page region containing the user navigation. */\n    GradingActions.prototype._region = null;\n\n    /**\n     * Show the actions if there is valid user.\n     *\n     * @method _showActionsForm\n     * @private\n     * @param {Event} event\n     * @param {Integer} userid\n     */\n    GradingActions.prototype._showActionsForm = function(event, userid) {\n        var form = this._region.find('[data-region=grading-actions-form]');\n\n        if (userid != this._lastUserId && userid > 0) {\n            this._lastUserId = userid;\n        }\n        if (userid > 0) {\n            form.removeClass('hide');\n        } else {\n            form.addClass('hide');\n        }\n\n    };\n\n    /**\n     * Trigger the named action.\n     *\n     * @method _trigger\n     * @private\n     * @param {String} action\n     */\n    GradingActions.prototype._trigger = function(action) {\n        $(document).trigger(action);\n    };\n\n    /**\n     * Get the review panel element.\n     *\n     * @method getReviewPanelElement\n     * @return {jQuery}\n     */\n    GradingActions.prototype.getReviewPanelElement = function() {\n        return $('[data-region=\"review-panel\"]');\n    };\n\n    /**\n     * Check if the page has a review panel.\n     *\n     * @method hasReviewPanelElement\n     * @return {bool}\n     */\n    GradingActions.prototype.hasReviewPanelElement = function() {\n        return this.getReviewPanelElement().length > 0;\n    };\n\n    /**\n     * Get the collapse grade panel button.\n     *\n     * @method getCollapseGradePanelButton\n     * @return {jQuery}\n     */\n    GradingActions.prototype.getCollapseGradePanelButton = function() {\n        return $('[data-region=\"grade-actions\"] .collapse-grade-panel');\n    };\n\n    /**\n     * Get the collapse review panel button.\n     *\n     * @method getCollapseReviewPanelButton\n     * @return {jQuery}\n     */\n    GradingActions.prototype.getCollapseReviewPanelButton = function() {\n        return $('[data-region=\"grade-actions\"] .collapse-review-panel');\n    };\n\n    /**\n     * Get the expand all panels button.\n     *\n     * @method getExpandAllPanelsButton\n     * @return {jQuery}\n     */\n    GradingActions.prototype.getExpandAllPanelsButton = function() {\n        return $('[data-region=\"grade-actions\"] .collapse-none');\n    };\n\n    /**\n     * Remove the active state from all layout buttons.\n     *\n     * @method resetLayoutButtons\n     */\n    GradingActions.prototype.resetLayoutButtons = function() {\n        this.getCollapseGradePanelButton().removeClass('active');\n        this.getCollapseReviewPanelButton().removeClass('active');\n        this.getExpandAllPanelsButton().removeClass('active');\n    };\n\n    /**\n     * Hide the review panel.\n     *\n     * @method collapseReviewPanel\n     */\n    GradingActions.prototype.collapseReviewPanel = function() {\n        $(document).trigger(GradingEvents.COLLAPSE_REVIEW_PANEL);\n        $(document).trigger(GradingEvents.EXPAND_GRADE_PANEL);\n        this.resetLayoutButtons();\n        this.getCollapseReviewPanelButton().addClass('active');\n    };\n\n    /**\n     * Show/Hide the grade panel.\n     *\n     * @method collapseGradePanel\n     */\n    GradingActions.prototype.collapseGradePanel = function() {\n        $(document).trigger(GradingEvents.COLLAPSE_GRADE_PANEL);\n        $(document).trigger(GradingEvents.EXPAND_REVIEW_PANEL);\n        this.resetLayoutButtons();\n        this.getCollapseGradePanelButton().addClass('active');\n    };\n\n    /**\n     * Return the layout to default.\n     *\n     * @method expandAllPanels\n     */\n    GradingActions.prototype.expandAllPanels = function() {\n        $(document).trigger(GradingEvents.EXPAND_GRADE_PANEL);\n        $(document).trigger(GradingEvents.EXPAND_REVIEW_PANEL);\n        this.resetLayoutButtons();\n        this.getExpandAllPanelsButton().addClass('active');\n    };\n\n    /**\n     * Register event listeners for the grade panel.\n     *\n     * @method registerEventListeners\n     */\n    GradingActions.prototype.registerEventListeners = function() {\n        // Don't need layout controls if there is no review panel.\n        if (this.hasReviewPanelElement()) {\n            var collapseReviewPanelButton = this.getCollapseReviewPanelButton();\n            collapseReviewPanelButton.click(function(e) {\n                this.collapseReviewPanel();\n                e.preventDefault();\n            }.bind(this));\n\n            collapseReviewPanelButton.keydown(function(e) {\n                if (!e.metaKey && !e.shiftKey && !e.altKey && !e.ctrlKey) {\n                    if (e.keyCode === 13 || e.keyCode === 32) {\n                        this.collapseReviewPanel();\n                        e.preventDefault();\n                    }\n                }\n            }.bind(this));\n\n            var collapseGradePanelButton = this.getCollapseGradePanelButton();\n            collapseGradePanelButton.click(function(e) {\n                this.collapseGradePanel();\n                e.preventDefault();\n            }.bind(this));\n\n            collapseGradePanelButton.keydown(function(e) {\n                if (!e.metaKey && !e.shiftKey && !e.altKey && !e.ctrlKey) {\n                    if (e.keyCode === 13 || e.keyCode === 32) {\n                        this.collapseGradePanel();\n                        e.preventDefault();\n                    }\n                }\n            }.bind(this));\n\n            var expandAllPanelsButton = this.getExpandAllPanelsButton();\n            expandAllPanelsButton.click(function(e) {\n                this.expandAllPanels();\n                e.preventDefault();\n            }.bind(this));\n\n            expandAllPanelsButton.keydown(function(e) {\n                if (!e.metaKey && !e.shiftKey && !e.altKey && !e.ctrlKey) {\n                    if (e.keyCode === 13 || e.keyCode === 32) {\n                        this.expandAllPanels();\n                        e.preventDefault();\n                    }\n                }\n            }.bind(this));\n        }\n\n        $(document).on('user-changed', this._showActionsForm.bind(this));\n\n        this._region.find('[name=\"savechanges\"]').on('click', this._trigger.bind(this, 'save-changes'));\n        this._region.find('[name=\"saveandshownext\"]').on('click', this._trigger.bind(this, 'save-and-show-next'));\n        this._region.find('[name=\"resetbutton\"]').on('click', this._trigger.bind(this, 'reset'));\n        this._region.find('form').on('submit', function(e) {\n            e.preventDefault();\n        });\n    };\n\n    return GradingActions;\n});\n"], "names": ["define", "$", "GradingEvents", "GradingActions", "selector", "_regionSelector", "_region", "registerEventListeners", "prototype", "_lastUserId", "_showActionsForm", "event", "userid", "form", "this", "find", "removeClass", "addClass", "_trigger", "action", "document", "trigger", "getReviewPanelElement", "hasReviewPanelElement", "length", "getCollapseGradePanelButton", "getCollapseReviewPanelButton", "getExpandAllPanelsButton", "resetLayoutButtons", "collapseReviewPanel", "COLLAPSE_REVIEW_PANEL", "EXPAND_GRADE_PANEL", "collapseGradePanel", "COLLAPSE_GRADE_PANEL", "EXPAND_REVIEW_PANEL", "expandAllPanels", "collapseReviewPanelButton", "click", "e", "preventDefault", "bind", "keydown", "metaKey", "shift<PERSON>ey", "altKey", "ctrl<PERSON>ey", "keyCode", "collapseGradePanelButton", "expandAllPanelsButton", "on"], "mappings": ";;;;;;;;AAuBAA,oCAAO,CAAC,SAAU,8BAA8B,SAASC,EAAGC,mBAQpDC,eAAiB,SAASC,eACrBC,gBAAkBD,cAClBE,QAAUL,EAAEG,eAEZG,iCAITJ,eAAeK,UAAUH,gBAAkB,KAG3CF,eAAeK,UAAUC,YAAc,EAGvCN,eAAeK,UAAUF,QAAU,KAUnCH,eAAeK,UAAUE,iBAAmB,SAASC,MAAOC,YACpDC,KAAOC,KAAKR,QAAQS,KAAK,sCAEzBH,QAAUE,KAAKL,aAAeG,OAAS,SAClCH,YAAcG,QAEnBA,OAAS,EACTC,KAAKG,YAAY,QAEjBH,KAAKI,SAAS,SAYtBd,eAAeK,UAAUU,SAAW,SAASC,QACzClB,EAAEmB,UAAUC,QAAQF,SASxBhB,eAAeK,UAAUc,sBAAwB,kBACtCrB,EAAE,iCASbE,eAAeK,UAAUe,sBAAwB,kBACtCT,KAAKQ,wBAAwBE,OAAS,GASjDrB,eAAeK,UAAUiB,4BAA8B,kBAC5CxB,EAAE,wDASbE,eAAeK,UAAUkB,6BAA+B,kBAC7CzB,EAAE,yDASbE,eAAeK,UAAUmB,yBAA2B,kBACzC1B,EAAE,iDAQbE,eAAeK,UAAUoB,mBAAqB,gBACrCH,8BAA8BT,YAAY,eAC1CU,+BAA+BV,YAAY,eAC3CW,2BAA2BX,YAAY,WAQhDb,eAAeK,UAAUqB,oBAAsB,WAC3C5B,EAAEmB,UAAUC,QAAQnB,cAAc4B,uBAClC7B,EAAEmB,UAAUC,QAAQnB,cAAc6B,yBAC7BH,0BACAF,+BAA+BT,SAAS,WAQjDd,eAAeK,UAAUwB,mBAAqB,WAC1C/B,EAAEmB,UAAUC,QAAQnB,cAAc+B,sBAClChC,EAAEmB,UAAUC,QAAQnB,cAAcgC,0BAC7BN,0BACAH,8BAA8BR,SAAS,WAQhDd,eAAeK,UAAU2B,gBAAkB,WACvClC,EAAEmB,UAAUC,QAAQnB,cAAc6B,oBAClC9B,EAAEmB,UAAUC,QAAQnB,cAAcgC,0BAC7BN,0BACAD,2BAA2BV,SAAS,WAQ7Cd,eAAeK,UAAUD,uBAAyB,cAE1CO,KAAKS,wBAAyB,KAC1Ba,0BAA4BtB,KAAKY,+BACrCU,0BAA0BC,MAAM,SAASC,QAChCT,sBACLS,EAAEC,kBACJC,KAAK1B,OAEPsB,0BAA0BK,QAAQ,SAASH,GAClCA,EAAEI,SAAYJ,EAAEK,UAAaL,EAAEM,QAAWN,EAAEO,SAC3B,KAAdP,EAAEQ,SAAgC,KAAdR,EAAEQ,eACjBjB,sBACLS,EAAEC,mBAGZC,KAAK1B,WAEHiC,yBAA2BjC,KAAKW,8BACpCsB,yBAAyBV,MAAM,SAASC,QAC/BN,qBACLM,EAAEC,kBACJC,KAAK1B,OAEPiC,yBAAyBN,QAAQ,SAASH,GACjCA,EAAEI,SAAYJ,EAAEK,UAAaL,EAAEM,QAAWN,EAAEO,SAC3B,KAAdP,EAAEQ,SAAgC,KAAdR,EAAEQ,eACjBd,qBACLM,EAAEC,mBAGZC,KAAK1B,WAEHkC,sBAAwBlC,KAAKa,2BACjCqB,sBAAsBX,MAAM,SAASC,QAC5BH,kBACLG,EAAEC,kBACJC,KAAK1B,OAEPkC,sBAAsBP,QAAQ,SAASH,GAC9BA,EAAEI,SAAYJ,EAAEK,UAAaL,EAAEM,QAAWN,EAAEO,SAC3B,KAAdP,EAAEQ,SAAgC,KAAdR,EAAEQ,eACjBX,kBACLG,EAAEC,mBAGZC,KAAK1B,OAGXb,EAAEmB,UAAU6B,GAAG,eAAgBnC,KAAKJ,iBAAiB8B,KAAK1B,YAErDR,QAAQS,KAAK,wBAAwBkC,GAAG,QAASnC,KAAKI,SAASsB,KAAK1B,KAAM,sBAC1ER,QAAQS,KAAK,4BAA4BkC,GAAG,QAASnC,KAAKI,SAASsB,KAAK1B,KAAM,4BAC9ER,QAAQS,KAAK,wBAAwBkC,GAAG,QAASnC,KAAKI,SAASsB,KAAK1B,KAAM,eAC1ER,QAAQS,KAAK,QAAQkC,GAAG,UAAU,SAASX,GAC5CA,EAAEC,qBAIHpC"}