{"version": 3, "file": "grading_events.min.js", "sources": ["../src/grading_events.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Events for the grading interface.\n *\n * @module     mod_assign/grading_events\n * @copyright  2016 Ryan Wyllie <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n * @since      3.1\n */\ndefine(function() {\n    return {\n        COLLAPSE_REVIEW_PANEL: 'grading:collapse-review-panel',\n        EXPAND_REVIEW_PANEL: 'grading:expand-review-panel',\n        COLLAPSE_GRADE_PANEL: 'grading:collapse-grade-panel',\n        EXPAND_GRADE_PANEL: 'grading:expand-grade-panel',\n    };\n});\n"], "names": ["define", "COLLAPSE_REVIEW_PANEL", "EXPAND_REVIEW_PANEL", "COLLAPSE_GRADE_PANEL", "EXPAND_GRADE_PANEL"], "mappings": ";;;;;;;;AAuBAA,oCAAO,iBACI,CACHC,sBAAuB,gCACvBC,oBAAqB,8BACrBC,qBAAsB,+BACtBC,mBAAoB"}