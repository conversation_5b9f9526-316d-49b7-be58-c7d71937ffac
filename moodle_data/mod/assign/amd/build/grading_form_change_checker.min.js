/**
 * Simple method to check for changes to a form between two points in time.
 *
 * @module     mod_assign/grading_form_change_checker
 * @copyright  2016 Damyon Wiese <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 * @since      3.1
 */
define("mod_assign/grading_form_change_checker",["jquery"],(function($){return{saveFormState:function(selector){$(selector).trigger("save-form-state");var data=$(selector).serialize();$(selector).data("saved-form-state",data)},checkFormForChanges:function(selector){$(selector).trigger("save-form-state");var data=$(selector).serialize(),previousdata=$(selector).data("saved-form-state");return!!$(selector).data("unresolved-error")||void 0!==previousdata&&previousdata!=data}}}));

//# sourceMappingURL=grading_form_change_checker.min.js.map