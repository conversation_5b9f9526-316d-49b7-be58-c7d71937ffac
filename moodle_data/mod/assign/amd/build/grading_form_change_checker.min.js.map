{"version": 3, "file": "grading_form_change_checker.min.js", "sources": ["../src/grading_form_change_checker.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Simple method to check for changes to a form between two points in time.\n *\n * @module     mod_assign/grading_form_change_checker\n * @copyright  2016 Damyon Wiese <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n * @since      3.1\n */\ndefine(['jquery'], function($) {\n\n    return {\n        /**\n         * Save the values in the form to a data attribute so they can be compared later for changes.\n         *\n         * @method saveFormState\n         * @param {String} selector The selector for the form element.\n         */\n        saveFormState: function(selector) {\n            $(selector).trigger('save-form-state');\n            var data = $(selector).serialize();\n            $(selector).data('saved-form-state', data);\n        },\n\n        /**\n         * Compare the current values in the form to the previously saved state.\n         *\n         * @method checkFormForChanges\n         * @param {String} selector The selector for the form element.\n         * @return {Boolean} True if there are changes to the form data.\n         */\n        checkFormForChanges: function(selector) {\n\n            $(selector).trigger('save-form-state');\n\n            var data = $(selector).serialize(),\n                previousdata = $(selector).data('saved-form-state'),\n                unresolvederror = $(selector).data('unresolved-error');\n\n            if (unresolvederror) {\n                return true;\n            }\n            if (typeof previousdata === 'undefined') {\n                return false;\n            }\n            return (previousdata != data);\n        }\n    };\n});\n"], "names": ["define", "$", "saveFormState", "selector", "trigger", "data", "serialize", "checkFormForChanges", "previousdata"], "mappings": ";;;;;;;;AAuBAA,gDAAO,CAAC,WAAW,SAASC,SAEjB,CAOHC,cAAe,SAASC,UACpBF,EAAEE,UAAUC,QAAQ,uBAChBC,KAAOJ,EAAEE,UAAUG,YACvBL,EAAEE,UAAUE,KAAK,mBAAoBA,OAUzCE,oBAAqB,SAASJ,UAE1BF,EAAEE,UAAUC,QAAQ,uBAEhBC,KAAOJ,EAAEE,UAAUG,YACnBE,aAAeP,EAAEE,UAAUE,KAAK,4BACdJ,EAAEE,UAAUE,KAAK,0BAKX,IAAjBG,cAGHA,cAAgBH"}