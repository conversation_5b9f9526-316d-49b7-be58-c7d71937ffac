/**
 * Javascript controller for the "User summary" panel at the top of the page.
 *
 * @module     mod_assign/grading_navigation_user_info
 * @copyright  2016 Damyon Wiese <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 * @since      3.1
 */
define("mod_assign/grading_navigation_user_info",["jquery","core/notification","core/ajax","core/templates"],(function($,notification,ajax,templates){var UserInfo=function(selector){this._regionSelector=selector,this._region=$(selector),this._userCache={},$(document).on("user-changed",this._refreshUserInfo.bind(this))};return UserInfo.prototype._regionSelector=null,UserInfo.prototype._userCache=null,UserInfo.prototype._region=null,UserInfo.prototype._lastUserId=0,UserInfo.prototype._getAssignmentId=function(){return this._region.attr("data-assignmentid")},UserInfo.prototype._refreshUserInfo=function(event,userid){var promise=$.Deferred();this._region.attr("data-userid",userid),this._lastUserId!=userid&&(this._lastUserId=userid,templates.render("mod_assign/loading",{}).done(function(html,js){if(this._region.fadeOut("fast",function(){templates.replaceNodeContents(this._region,html,js),this._region.fadeIn("fast")}.bind(this)),userid<0)templates.render("mod_assign/grading_navigation_no_users",{}).done(function(html,js){userid==this._lastUserId&&this._region.fadeOut("fast",function(){templates.replaceNodeContents(this._region,html,js),this._region.fadeIn("fast")}.bind(this))}.bind(this)).fail(notification.exception);else{if(void 0!==this._userCache[userid])promise.resolve(this._userCache[userid]);else{var assignmentId=this._getAssignmentId();ajax.call([{methodname:"mod_assign_get_participant",args:{userid:userid,assignid:assignmentId,embeduser:!0}}])[0].done(function(participant){participant.hasOwnProperty("id")?(this._userCache[userid]=participant,promise.resolve(this._userCache[userid])):promise.reject("No users")}.bind(this)).fail(notification.exception)}promise.done(function(context){var identityfields=$("[data-showuseridentity]").data("showuseridentity").split(","),identity=[];context.courseid=$('[data-region="grading-navigation-panel"]').attr("data-courseid"),context.user&&($.each(identityfields,(function(i,k){void 0!==context.user[k]&&""!==context.user[k]&&(context.hasidentity=!0,identity.push(context.user[k]))})),context.identity=identity.join(", "),context.user.profileimageurl&&(context.profileimageurl=context.user.profileimageurl)),templates.render("mod_assign/grading_navigation_user_summary",context).done(function(html,js){userid==this._lastUserId&&this._region.fadeOut("fast",function(){templates.replaceNodeContents(this._region,html,js),this._region.fadeIn("fast")}.bind(this))}.bind(this)).fail(notification.exception)}.bind(this)).fail(function(){templates.render("mod_assign/grading_navigation_no_users",{}).done(function(html,js){this._region.fadeOut("fast",function(){templates.replaceNodeContents(this._region,html,js),this._region.fadeIn("fast")}.bind(this))}.bind(this)).fail(notification.exception)}.bind(this))}}.bind(this)).fail(notification.exception))},UserInfo}));

//# sourceMappingURL=grading_navigation_user_info.min.js.map