{"version": 3, "file": "grading_navigation_user_info.min.js", "sources": ["../src/grading_navigation_user_info.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Javascript controller for the \"User summary\" panel at the top of the page.\n *\n * @module     mod_assign/grading_navigation_user_info\n * @copyright  2016 Damyon Wiese <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n * @since      3.1\n */\ndefine(['jquery', 'core/notification', 'core/ajax', 'core/templates'], function($, notification, ajax, templates) {\n\n    /**\n     * UserInfo class.\n     *\n     * @class mod_assign/grading_navigation_user_info\n     * @param {String} selector The selector for the page region containing the user navigation.\n     */\n    var UserInfo = function(selector) {\n        this._regionSelector = selector;\n        this._region = $(selector);\n        this._userCache = {};\n\n        $(document).on('user-changed', this._refreshUserInfo.bind(this));\n    };\n\n    /** @property {String} Selector for the page region containing the user navigation. */\n    UserInfo.prototype._regionSelector = null;\n\n    /** @property {Array} Cache of user info contexts. */\n    UserInfo.prototype._userCache = null;\n\n    /** @property {JQuery} JQuery node for the page region containing the user navigation. */\n    UserInfo.prototype._region = null;\n\n    /** @property {Integer} Remember the last user id to prevent unnecessary reloads. */\n    UserInfo.prototype._lastUserId = 0;\n\n    /**\n     * Get the assignment id\n     *\n     * @private\n     * @method _getAssignmentId\n     * @return {Integer} assignment id\n     */\n    UserInfo.prototype._getAssignmentId = function() {\n        return this._region.attr('data-assignmentid');\n    };\n\n    /**\n     * Get the user context - re-render the template in the page.\n     *\n     * @private\n     * @method _refreshUserInfo\n     * @param {Event} event\n     * @param {Number} userid\n     */\n    UserInfo.prototype._refreshUserInfo = function(event, userid) {\n        var promise = $.Deferred();\n\n        // Put the current user ID in the DOM so yui can access it.\n        this._region.attr('data-userid', userid);\n\n        // Skip reloading if it is the same user.\n        if (this._lastUserId == userid) {\n            return;\n        }\n        this._lastUserId = userid;\n\n        // First insert the loading template.\n        templates.render('mod_assign/loading', {}).done(function(html, js) {\n            // Update the page.\n            this._region.fadeOut(\"fast\", function() {\n                templates.replaceNodeContents(this._region, html, js);\n                this._region.fadeIn(\"fast\");\n            }.bind(this));\n\n            if (userid < 0) {\n                // Render the template.\n                templates.render('mod_assign/grading_navigation_no_users', {}).done(function(html, js) {\n                    if (userid == this._lastUserId) {\n                        // Update the page.\n                        this._region.fadeOut(\"fast\", function() {\n                            templates.replaceNodeContents(this._region, html, js);\n                            this._region.fadeIn(\"fast\");\n                        }.bind(this));\n                    }\n                }.bind(this)).fail(notification.exception);\n                return;\n            }\n\n            if (typeof this._userCache[userid] !== \"undefined\") {\n                promise.resolve(this._userCache[userid]);\n            } else {\n                // Load context from ajax.\n                var assignmentId = this._getAssignmentId();\n                var requests = ajax.call([{\n                    methodname: 'mod_assign_get_participant',\n                    args: {\n                        userid: userid,\n                        assignid: assignmentId,\n                        embeduser: true\n                    }\n                }]);\n\n                requests[0].done(function(participant) {\n                    if (!participant.hasOwnProperty('id')) {\n                        promise.reject('No users');\n                    } else {\n                        this._userCache[userid] = participant;\n                        promise.resolve(this._userCache[userid]);\n                    }\n                }.bind(this)).fail(notification.exception);\n            }\n\n            promise.done(function(context) {\n                var identityfields = $('[data-showuseridentity]').data('showuseridentity').split(','),\n                    identity = [];\n                // Render the template.\n                context.courseid = $('[data-region=\"grading-navigation-panel\"]').attr('data-courseid');\n\n                if (context.user) {\n                    // Build a string for the visible identity fields listed in showuseridentity config setting.\n                    $.each(identityfields, function(i, k) {\n                        if (typeof context.user[k] !== 'undefined' && context.user[k] !== '') {\n                            context.hasidentity = true;\n                            identity.push(context.user[k]);\n                        }\n                    });\n                    context.identity = identity.join(', ');\n\n                    // Add profile image url to context.\n                    if (context.user.profileimageurl) {\n                        context.profileimageurl = context.user.profileimageurl;\n                    }\n                }\n\n                templates.render('mod_assign/grading_navigation_user_summary', context).done(function(html, js) {\n                    // Update the page.\n                    if (userid == this._lastUserId) {\n                        this._region.fadeOut(\"fast\", function() {\n                            templates.replaceNodeContents(this._region, html, js);\n                            this._region.fadeIn(\"fast\");\n                        }.bind(this));\n                    }\n                }.bind(this)).fail(notification.exception);\n            }.bind(this)).fail(function() {\n                // Render the template.\n                templates.render('mod_assign/grading_navigation_no_users', {}).done(function(html, js) {\n                    // Update the page.\n                    this._region.fadeOut(\"fast\", function() {\n                        templates.replaceNodeContents(this._region, html, js);\n                        this._region.fadeIn(\"fast\");\n                    }.bind(this));\n                }.bind(this)).fail(notification.exception);\n            }\n            .bind(this));\n        }.bind(this)).fail(notification.exception);\n    };\n\n    return UserInfo;\n});\n"], "names": ["define", "$", "notification", "ajax", "templates", "UserInfo", "selector", "_regionSelector", "_region", "_userCache", "document", "on", "this", "_refreshUserInfo", "bind", "prototype", "_lastUserId", "_getAssignmentId", "attr", "event", "userid", "promise", "Deferred", "render", "done", "html", "js", "fadeOut", "replaceNodeContents", "fadeIn", "fail", "exception", "resolve", "assignmentId", "call", "methodname", "args", "assignid", "embeduser", "participant", "hasOwnProperty", "reject", "context", "identityfields", "data", "split", "identity", "courseid", "user", "each", "i", "k", "hasidentity", "push", "join", "profileimageurl"], "mappings": ";;;;;;;;AAuBAA,iDAAO,CAAC,SAAU,oBAAqB,YAAa,mBAAmB,SAASC,EAAGC,aAAcC,KAAMC,eAQ/FC,SAAW,SAASC,eACfC,gBAAkBD,cAClBE,QAAUP,EAAEK,eACZG,WAAa,GAElBR,EAAES,UAAUC,GAAG,eAAgBC,KAAKC,iBAAiBC,KAAKF,eAI9DP,SAASU,UAAUR,gBAAkB,KAGrCF,SAASU,UAAUN,WAAa,KAGhCJ,SAASU,UAAUP,QAAU,KAG7BH,SAASU,UAAUC,YAAc,EASjCX,SAASU,UAAUE,iBAAmB,kBAC3BL,KAAKJ,QAAQU,KAAK,sBAW7Bb,SAASU,UAAUF,iBAAmB,SAASM,MAAOC,YAC9CC,QAAUpB,EAAEqB,gBAGXd,QAAQU,KAAK,cAAeE,QAG7BR,KAAKI,aAAeI,cAGnBJ,YAAcI,OAGnBhB,UAAUmB,OAAO,qBAAsB,IAAIC,KAAK,SAASC,KAAMC,YAEtDlB,QAAQmB,QAAQ,OAAQ,WACzBvB,UAAUwB,oBAAoBhB,KAAKJ,QAASiB,KAAMC,SAC7ClB,QAAQqB,OAAO,SACtBf,KAAKF,OAEHQ,OAAS,EAEThB,UAAUmB,OAAO,yCAA0C,IAAIC,KAAK,SAASC,KAAMC,IAC3EN,QAAUR,KAAKI,kBAEVR,QAAQmB,QAAQ,OAAQ,WACzBvB,UAAUwB,oBAAoBhB,KAAKJ,QAASiB,KAAMC,SAC7ClB,QAAQqB,OAAO,SACtBf,KAAKF,QAEbE,KAAKF,OAAOkB,KAAK5B,aAAa6B,wBAIG,IAA5BnB,KAAKH,WAAWW,QACvBC,QAAQW,QAAQpB,KAAKH,WAAWW,aAC7B,KAECa,aAAerB,KAAKK,mBACTd,KAAK+B,KAAK,CAAC,CACtBC,WAAY,6BACZC,KAAM,CACFhB,OAAQA,OACRiB,SAAUJ,aACVK,WAAW,MAIV,GAAGd,KAAK,SAASe,aACjBA,YAAYC,eAAe,YAGvB/B,WAAWW,QAAUmB,YAC1BlB,QAAQW,QAAQpB,KAAKH,WAAWW,UAHhCC,QAAQoB,OAAO,aAKrB3B,KAAKF,OAAOkB,KAAK5B,aAAa6B,WAGpCV,QAAQG,KAAK,SAASkB,aACdC,eAAiB1C,EAAE,2BAA2B2C,KAAK,oBAAoBC,MAAM,KAC7EC,SAAW,GAEfJ,QAAQK,SAAW9C,EAAE,4CAA4CiB,KAAK,iBAElEwB,QAAQM,OAER/C,EAAEgD,KAAKN,gBAAgB,SAASO,EAAGC,QACA,IAApBT,QAAQM,KAAKG,IAA0C,KAApBT,QAAQM,KAAKG,KACvDT,QAAQU,aAAc,EACtBN,SAASO,KAAKX,QAAQM,KAAKG,QAGnCT,QAAQI,SAAWA,SAASQ,KAAK,MAG7BZ,QAAQM,KAAKO,kBACbb,QAAQa,gBAAkBb,QAAQM,KAAKO,kBAI/CnD,UAAUmB,OAAO,6CAA8CmB,SAASlB,KAAK,SAASC,KAAMC,IAEpFN,QAAUR,KAAKI,kBACVR,QAAQmB,QAAQ,OAAQ,WACzBvB,UAAUwB,oBAAoBhB,KAAKJ,QAASiB,KAAMC,SAC7ClB,QAAQqB,OAAO,SACtBf,KAAKF,QAEbE,KAAKF,OAAOkB,KAAK5B,aAAa6B,YAClCjB,KAAKF,OAAOkB,KAAK,WAEf1B,UAAUmB,OAAO,yCAA0C,IAAIC,KAAK,SAASC,KAAMC,SAE1ElB,QAAQmB,QAAQ,OAAQ,WACzBvB,UAAUwB,oBAAoBhB,KAAKJ,QAASiB,KAAMC,SAC7ClB,QAAQqB,OAAO,SACtBf,KAAKF,QACTE,KAAKF,OAAOkB,KAAK5B,aAAa6B,YAEnCjB,KAAKF,SACRE,KAAKF,OAAOkB,KAAK5B,aAAa6B,aAG7B1B"}