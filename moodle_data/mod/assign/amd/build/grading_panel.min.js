/**
 * Javascript controller for the "Grading" panel at the right of the page.
 *
 * @module     mod_assign/grading_panel
 * @copyright  2016 <PERSON>yon Wiese <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 * @since      3.1
 */
define("mod_assign/grading_panel",["jquery","core/yui","core/notification","core/templates","core/fragment","core/ajax","core/str","mod_assign/grading_form_change_checker","mod_assign/grading_events","core_form/events","core/toast","core_form/changechecker"],(function($,Y,notification,templates,fragment,ajax,str,checker,GradingEvents,FormEvents,Toast,FormChangeChecker){var GradingPanel=function(selector){this._regionSelector=selector,this._region=$(selector),this._userCache=[],this.registerEventListeners()};return GradingPanel.prototype._regionSelector=null,GradingPanel.prototype._lastUserId=0,GradingPanel.prototype._lastAttemptNumber=-1,GradingPanel.prototype._region=null,GradingPanel.prototype.nextUserId=null,GradingPanel.prototype.nextUser=!1,GradingPanel.prototype._niceReplaceNodeContents=function(node,html,js){var promise=$.Deferred();return node.fadeOut("fast",(function(){templates.replaceNodeContents(node,html,js),node.fadeIn("fast",(function(){promise.resolve()}))})),promise.promise()},GradingPanel.prototype._saveFormState=function(){var checked=$('[data-region="grading-actions-form"] [name="sendstudentnotifications"]').prop("checked");$('.gradeform [name="sendstudentnotifications"]').val(checked)},GradingPanel.prototype._submitForm=function(event,nextUserId,nextUser){var commentAreaElement=document.querySelector(".comment-area");commentAreaElement&&(""!==commentAreaElement.querySelector(".db > textarea").value&&commentAreaElement.querySelector('.fd a[id^="comment-action-post-"]').click());var form=$(this._region.find("form.gradeform"));$('[data-region="overlay"]').show(),FormChangeChecker.markFormSubmitted(form[0]),form.trigger("save-form-state"),FormEvents.notifyFormSubmittedByJavascript(form[0]);var data=form.serialize(),assignmentid=this._region.attr("data-assignmentid");ajax.call([{methodname:"mod_assign_submit_grading_form",args:{assignmentid:assignmentid,userid:this._lastUserId,jsonformdata:JSON.stringify(data)},done:this._handleFormSubmissionResponse.bind(this,data,nextUserId,nextUser),fail:notification.exception}])},GradingPanel.prototype._handleFormSubmissionResponse=function(formdata,nextUserId,nextUser,response){if(void 0===nextUserId&&(nextUserId=this._lastUserId),response.length)str.get_string("errorgradechangessaveddetail","mod_assign").then((function(str){return Toast.add(str,{type:"danger",delay:4e3}),str})).catch(notification.exception),$(document).trigger("reset",[this._lastUserId,formdata,!0]);else{str.get_string("gradechangessaveddetail","mod_assign").then((function(str){return Toast.add(str),str})).catch(notification.exception);var form=$(this._region.find("form.gradeform"));FormChangeChecker.resetFormDirtyState(form[0]),nextUserId==this._lastUserId?$(document).trigger("reset",nextUserId):nextUser?$(document).trigger("done-saving-show-next",!0):$(document).trigger("user-changed",nextUserId)}$('[data-region="overlay"]').hide()},GradingPanel.prototype._resetForm=function(e,userid,formdata,unresolvederror){var event=$.Event("custom");void 0===userid&&(userid=this._lastUserId),this._lastUserId=0,this._refreshGradingPanel(event,userid,formdata,-1,unresolvederror)},GradingPanel.prototype._chooseAttempt=function(e){var submissionsId=$(e.target).data("submissions"),formhtml=$(document.getElementById(submissionsId)).clone().wrap($("<form/>")).html();str.get_strings([{key:"viewadifferentattempt",component:"mod_assign"},{key:"view",component:"core"},{key:"cancel",component:"core"}]).done(function(strs){notification.confirm(strs[0],formhtml,strs[1],strs[2],function(){var attemptnumber=$("input:radio[name='select-attemptnumber']:checked").val();this._refreshGradingPanel(null,this._lastUserId,"",attemptnumber)}.bind(this))}.bind(this)).fail(notification.exception)},GradingPanel.prototype._addPopoutButtons=function(selector){var region=$(selector);templates.render("mod_assign/popout_button",{}).done(function(html){region.find('[data-fieldtype="filemanager"],[data-fieldtype="editor"],[data-fieldtype="grading"]').closest(".fitem").addClass("has-popout").find("label:first").parent().append(html),region.on("click",'[data-region="popout-button"]',this._togglePopout.bind(this))}.bind(this)).fail(notification.exception)},GradingPanel.prototype._togglePopout=function(event){event.preventDefault();var container=$(event.target).closest(".fitem");container.hasClass("popout")?$(".popout").removeClass("popout"):($(".popout").removeClass("popout"),container.addClass("popout"),container.addClass("moodle-has-zindex"))},GradingPanel.prototype._refreshGradingPanel=function(event,userid,submissiondata,attemptnumber,unresolvederror){var contextid=this._region.attr("data-contextid");void 0===submissiondata&&(submissiondata=""),void 0===attemptnumber&&(attemptnumber=-1),void 0===unresolvederror&&(unresolvederror=!1),this._lastUserId==userid&&this._lastAttemptNumber==attemptnumber&&""===submissiondata||(this._lastUserId=userid,this._lastAttemptNumber=attemptnumber,$(document).trigger("start-loading-user"),window.M.util.js_pending("mod-assign-loading-user"),templates.render("mod_assign/loading",{}).done(function(html,js){this._niceReplaceNodeContents(this._region,html,js).done(function(){if(userid>0){this._region.show();var params={userid:userid,attemptnumber:attemptnumber,jsonformdata:JSON.stringify(submissiondata)};fragment.loadFragment("mod_assign","gradingpanel",contextid,params).done(function(html,js){""===html&&$(document).trigger("reset-table",!0),this._niceReplaceNodeContents(this._region,html,js).done(function(){checker.saveFormState('[data-region="grade-panel"] .gradeform'),$(document).on("editor-content-restored",(function(){checker.saveFormState('[data-region="grade-panel"] .gradeform')})),$('[data-region="attempt-chooser"]').on("click",this._chooseAttempt.bind(this)),this._addPopoutButtons('[data-region="grade-panel"] .gradeform'),unresolvederror&&$('[data-region="grade-panel"] .gradeform').data("unresolved-error",!0),$(document).trigger("finish-loading-user"),window.M.util.js_complete("mod-assign-loading-user")}.bind(this)).fail(notification.exception)}.bind(this)).fail(notification.exception),$('[data-region="review-panel"]').show()}else this._region.hide(),$('[data-region="review-panel"]').hide(),$(document).trigger("finish-loading-user"),window.M.util.js_complete("mod-assign-loading-user")}.bind(this))}.bind(this)).fail(notification.exception))},GradingPanel.prototype._getNextUser=function(event,data){this.nextUserId=data.nextUserId,this.nextUser=data.nextUser},GradingPanel.prototype._handleSaveAndShowNext=function(){this._submitForm(null,this.nextUserId,this.nextUser)},GradingPanel.prototype.getPanelElement=function(){return $('[data-region="grade-panel"]')},GradingPanel.prototype.collapsePanel=function(){this.getPanelElement().addClass("collapsed")},GradingPanel.prototype.expandPanel=function(){this.getPanelElement().removeClass("collapsed")},GradingPanel.prototype.registerEventListeners=function(){var docElement=$(document);$(this._region).on("submit","form",(function(e){e.preventDefault()})),docElement.on("next-user",this._getNextUser.bind(this)),docElement.on("user-changed",this._refreshGradingPanel.bind(this)),docElement.on("save-changes",this._submitForm.bind(this)),docElement.on("save-and-show-next",this._handleSaveAndShowNext.bind(this)),docElement.on("reset",this._resetForm.bind(this)),docElement.on("save-form-state",this._saveFormState.bind(this)),docElement.on(GradingEvents.COLLAPSE_GRADE_PANEL,function(){this.collapsePanel()}.bind(this)),docElement.on(GradingEvents.COLLAPSE_REVIEW_PANEL,function(){this.expandPanel()}.bind(this)),docElement.on(GradingEvents.EXPAND_GRADE_PANEL,function(){this.expandPanel()}.bind(this))},GradingPanel}));

//# sourceMappingURL=grading_panel.min.js.map