/**
 * Javascript controller for the "Review" panel at the left of the page.
 *
 * @module     mod_assign/grading_review_panel
 * @copyright  2016 Damyon Wiese <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 * @since      3.1
 */
define("mod_assign/grading_review_panel",["jquery","mod_assign/grading_events"],(function($,GradingEvents){var GradingReviewPanel=function(){this._region=$('[data-region="review-panel-content"]'),this.registerEventListeners()};return GradingReviewPanel.prototype._region=null,GradingReviewPanel.prototype.getReviewPanel=function(pluginname){return void 0===this._region.data("panel-owner")&&this._region.data("review-panel-plugin",pluginname),this._region.data("review-panel-plugin")==pluginname&&this._region[0]},GradingReviewPanel.prototype.getTogglePanelButton=function(){return this.getPanelElement().find('[data-region="review-panel-toggle"]')},GradingReviewPanel.prototype.getPanelElement=function(){return $('[data-region="review-panel"]')},GradingReviewPanel.prototype.getPanelContentElement=function(){return $('[data-region="review-panel-content"]')},GradingReviewPanel.prototype.togglePanel=function(){this.getPanelElement().hasClass("collapsed")?$(document).trigger(GradingEvents.EXPAND_REVIEW_PANEL):$(document).trigger(GradingEvents.COLLAPSE_REVIEW_PANEL)},GradingReviewPanel.prototype.collapsePanel=function(){this.getPanelElement().addClass("collapsed").removeClass("grade-panel-collapsed"),this.getPanelContentElement().attr("aria-hidden",!0)},GradingReviewPanel.prototype.expandPanel=function(){this.getPanelElement().removeClass("collapsed"),this.getPanelContentElement().removeAttr("aria-hidden")},GradingReviewPanel.prototype.registerEventListeners=function(){var toggleReviewPanelButton=this.getTogglePanelButton();toggleReviewPanelButton.click(function(e){this.togglePanel(),e.preventDefault()}.bind(this)),toggleReviewPanelButton.keydown(function(e){e.metaKey||e.shiftKey||e.altKey||e.ctrlKey||13!==e.keyCode&&32!==e.keyCode||(this.togglePanel(),e.preventDefault())}.bind(this));var docElement=$(document);docElement.on(GradingEvents.COLLAPSE_REVIEW_PANEL,function(){this.collapsePanel()}.bind(this)),docElement.on(GradingEvents.COLLAPSE_GRADE_PANEL,function(){this.expandPanel(),this.getPanelElement().addClass("grade-panel-collapsed")}.bind(this)),docElement.on(GradingEvents.EXPAND_REVIEW_PANEL,function(){this.expandPanel()}.bind(this)),docElement.on(GradingEvents.EXPAND_GRADE_PANEL,function(){this.getPanelElement().removeClass("grade-panel-collapsed")}.bind(this))},GradingReviewPanel}));

//# sourceMappingURL=grading_review_panel.min.js.map