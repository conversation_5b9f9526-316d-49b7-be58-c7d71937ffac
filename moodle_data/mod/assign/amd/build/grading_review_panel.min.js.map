{"version": 3, "file": "grading_review_panel.min.js", "sources": ["../src/grading_review_panel.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Javascript controller for the \"Review\" panel at the left of the page.\n *\n * @module     mod_assign/grading_review_panel\n * @copyright  2016 Damyon Wiese <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n * @since      3.1\n */\ndefine(['jquery', 'mod_assign/grading_events'], function($, GradingEvents) {\n\n    /**\n     * GradingReviewPanel class.\n     *\n     * @class mod_assign/grading_review_panel\n     */\n    var GradingReviewPanel = function() {\n        this._region = $('[data-region=\"review-panel-content\"]');\n        this.registerEventListeners();\n    };\n\n    /** @property {JQuery} JQuery node for the page region containing the user navigation. */\n    GradingReviewPanel.prototype._region = null;\n\n    /**\n     * It is first come first served to get ownership of the grading review panel.\n     * There can be only one.\n     *\n     * @public\n     * @method getReviewPanel\n     * @param {String} pluginname - the first plugin to ask for the panel gets it.\n     * @return {DOMNode} or false\n     */\n    GradingReviewPanel.prototype.getReviewPanel = function(pluginname) {\n        var owner = this._region.data('panel-owner');\n        if (typeof owner == \"undefined\") {\n            this._region.data('review-panel-plugin', pluginname);\n        }\n        if (this._region.data('review-panel-plugin') == pluginname) {\n            return this._region[0];\n        }\n        return false;\n    };\n\n    /**\n     * Get the toggle review panel button.\n     *\n     * @method getTogglePanelButton\n     * @return {jQuery}\n     */\n    GradingReviewPanel.prototype.getTogglePanelButton = function() {\n        return this.getPanelElement().find('[data-region=\"review-panel-toggle\"]');\n    };\n\n    /**\n     * Get the review panel element.\n     *\n     * @method getPanelElement\n     * @return {jQuery}\n     */\n    GradingReviewPanel.prototype.getPanelElement = function() {\n        return $('[data-region=\"review-panel\"]');\n    };\n\n    /**\n     * Get the review panel content element.\n     *\n     * @method getPanelContentElement\n     * @return {jQuery}\n     */\n    GradingReviewPanel.prototype.getPanelContentElement = function() {\n        return $('[data-region=\"review-panel-content\"]');\n    };\n\n    /**\n     * Show/Hide the review panel.\n     *\n     * @method togglePanel\n     */\n    GradingReviewPanel.prototype.togglePanel = function() {\n        if (this.getPanelElement().hasClass('collapsed')) {\n            $(document).trigger(GradingEvents.EXPAND_REVIEW_PANEL);\n        } else {\n            $(document).trigger(GradingEvents.COLLAPSE_REVIEW_PANEL);\n        }\n    };\n\n    /**\n     * Hide the review panel.\n     *\n     * @method collapsePanel\n     */\n    GradingReviewPanel.prototype.collapsePanel = function() {\n        this.getPanelElement().addClass('collapsed').removeClass('grade-panel-collapsed');\n        this.getPanelContentElement().attr('aria-hidden', true);\n    };\n\n    /**\n     * Show the review panel.\n     *\n     * @method expandPanel\n     */\n    GradingReviewPanel.prototype.expandPanel = function() {\n        this.getPanelElement().removeClass('collapsed');\n        this.getPanelContentElement().removeAttr('aria-hidden');\n    };\n\n    /**\n     * Register event listeners for the review panel.\n     *\n     * @method registerEventListeners\n     */\n    GradingReviewPanel.prototype.registerEventListeners = function() {\n        var toggleReviewPanelButton = this.getTogglePanelButton();\n        toggleReviewPanelButton.click(function(e) {\n            this.togglePanel();\n            e.preventDefault();\n        }.bind(this));\n\n        toggleReviewPanelButton.keydown(function(e) {\n            if (!e.metaKey && !e.shiftKey && !e.altKey && !e.ctrlKey) {\n                if (e.keyCode === 13 || e.keyCode === 32) {\n                    this.togglePanel();\n                    e.preventDefault();\n                }\n            }\n        }.bind(this));\n\n        var docElement = $(document);\n        docElement.on(GradingEvents.COLLAPSE_REVIEW_PANEL, function() {\n            this.collapsePanel();\n        }.bind(this));\n\n        // Need special styling when grade panel is collapsed.\n        docElement.on(GradingEvents.COLLAPSE_GRADE_PANEL, function() {\n            this.expandPanel();\n            this.getPanelElement().addClass('grade-panel-collapsed');\n        }.bind(this));\n\n        docElement.on(GradingEvents.EXPAND_REVIEW_PANEL, function() {\n            this.expandPanel();\n        }.bind(this));\n\n        docElement.on(GradingEvents.EXPAND_GRADE_PANEL, function() {\n            this.getPanelElement().removeClass('grade-panel-collapsed');\n        }.bind(this));\n    };\n\n    return GradingReviewPanel;\n});\n"], "names": ["define", "$", "GradingEvents", "GradingReviewPanel", "_region", "registerEventListeners", "prototype", "getReviewPanel", "pluginname", "this", "data", "getTogglePanelButton", "getPanelElement", "find", "getPanelContentElement", "togglePanel", "hasClass", "document", "trigger", "EXPAND_REVIEW_PANEL", "COLLAPSE_REVIEW_PANEL", "collapsePanel", "addClass", "removeClass", "attr", "expandPanel", "removeAttr", "toggleReviewPanelButton", "click", "e", "preventDefault", "bind", "keydown", "metaKey", "shift<PERSON>ey", "altKey", "ctrl<PERSON>ey", "keyCode", "doc<PERSON><PERSON>", "on", "COLLAPSE_GRADE_PANEL", "EXPAND_GRADE_PANEL"], "mappings": ";;;;;;;;AAuBAA,yCAAO,CAAC,SAAU,8BAA8B,SAASC,EAAGC,mBAOpDC,mBAAqB,gBAChBC,QAAUH,EAAE,6CACZI,iCAITF,mBAAmBG,UAAUF,QAAU,KAWvCD,mBAAmBG,UAAUC,eAAiB,SAASC,wBAE/B,IADRC,KAAKL,QAAQM,KAAK,qBAErBN,QAAQM,KAAK,sBAAuBF,YAEzCC,KAAKL,QAAQM,KAAK,wBAA0BF,YACrCC,KAAKL,QAAQ,IAW5BD,mBAAmBG,UAAUK,qBAAuB,kBACzCF,KAAKG,kBAAkBC,KAAK,wCASvCV,mBAAmBG,UAAUM,gBAAkB,kBACpCX,EAAE,iCASbE,mBAAmBG,UAAUQ,uBAAyB,kBAC3Cb,EAAE,yCAQbE,mBAAmBG,UAAUS,YAAc,WACnCN,KAAKG,kBAAkBI,SAAS,aAChCf,EAAEgB,UAAUC,QAAQhB,cAAciB,qBAElClB,EAAEgB,UAAUC,QAAQhB,cAAckB,wBAS1CjB,mBAAmBG,UAAUe,cAAgB,gBACpCT,kBAAkBU,SAAS,aAAaC,YAAY,8BACpDT,yBAAyBU,KAAK,eAAe,IAQtDrB,mBAAmBG,UAAUmB,YAAc,gBAClCb,kBAAkBW,YAAY,kBAC9BT,yBAAyBY,WAAW,gBAQ7CvB,mBAAmBG,UAAUD,uBAAyB,eAC9CsB,wBAA0BlB,KAAKE,uBACnCgB,wBAAwBC,MAAM,SAASC,QAC9Bd,cACLc,EAAEC,kBACJC,KAAKtB,OAEPkB,wBAAwBK,QAAQ,SAASH,GAChCA,EAAEI,SAAYJ,EAAEK,UAAaL,EAAEM,QAAWN,EAAEO,SAC3B,KAAdP,EAAEQ,SAAgC,KAAdR,EAAEQ,eACjBtB,cACLc,EAAEC,mBAGZC,KAAKtB,WAEH6B,WAAarC,EAAEgB,UACnBqB,WAAWC,GAAGrC,cAAckB,sBAAuB,gBAC1CC,iBACPU,KAAKtB,OAGP6B,WAAWC,GAAGrC,cAAcsC,qBAAsB,gBACzCf,mBACAb,kBAAkBU,SAAS,0BAClCS,KAAKtB,OAEP6B,WAAWC,GAAGrC,cAAciB,oBAAqB,gBACxCM,eACPM,KAAKtB,OAEP6B,WAAWC,GAAGrC,cAAcuC,mBAAoB,gBACvC7B,kBAAkBW,YAAY,0BACrCQ,KAAKtB,QAGJN"}