{"version": 3, "file": "override_delete_modal.min.js", "sources": ["../src/override_delete_modal.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Modal for deleting an override with the option to recalculate penalties.\n *\n * @module     `mod_assign/override_delete_modal\n * @copyright  2025 Catalyst IT Australia Pty Ltd\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport * as CustomEvents from 'core/custom_interaction_events';\nimport Config from 'core/config';\nimport Modal from 'core/modal';\n\nconst SELECTORS = {\n    DELETE_BUTTONS: '.delete-override',\n    RECACULATION_CHECKBOX: '#recalculatepenalties',\n};\n\n/**\n * Custom Modal\n */\nexport default class OverrideDeleteModal extends Modal {\n    static TYPE = \"mod_assign/override_delete_modal\";\n    static TEMPLATE = \"mod_assign/override_delete_modal\";\n\n    /**\n     * Configure the modal.\n     *\n     * @param {Object} modalConfig\n     */\n    configure(modalConfig) {\n        // Add question modals are always large.\n        modalConfig.large = true;\n\n        // Always show on creation.\n        modalConfig.show = true;\n        modalConfig.removeOnClose = true;\n\n        // Apply standard configuration.\n        super.configure(modalConfig);\n\n        this.setOverrideId(modalConfig.overrideId);\n        this.setSessionKey(modalConfig.sessionKey);\n    }\n\n    /**\n     * Constructor.\n     * Set required data to null.\n     *\n     * @param {HTMLElement} root\n     */\n    constructor(root) {\n        super(root);\n\n        // Recalculate penalties checkbox.\n        this.recalculationCheckbox = this.getModal().find(SELECTORS.RECACULATION_CHECKBOX);\n\n        // Data.\n        this.setOverrideId(null);\n        this.setSessionKey(null);\n    }\n\n    /**\n     * Set the override id.\n     *\n     * @param {number} id The override id.\n     */\n    setOverrideId(id) {\n        this.overrideId = id;\n    }\n\n    /**\n     * Get the override id.\n     *\n     * @returns {*}\n     */\n    getOverrideId() {\n        return this.overrideId;\n    }\n\n    /**\n     * Set the session key.\n     *\n     * @param {string} key\n     */\n    setSessionKey(key) {\n        this.sessionKey = key;\n    }\n\n    /**\n     * Get the session key.\n     *\n     * @returns {*}\n     */\n    getSessionKey() {\n        return this.sessionKey;\n    }\n\n    /**\n     * Register events.\n     *\n     */\n    registerEventListeners() {\n        // Apply parent event listeners.\n        super.registerEventListeners(this);\n\n        // Register to close on cancel.\n        this.registerCloseOnCancel();\n\n        // Register the delete action.\n        this.getModal().on(CustomEvents.events.activate, this.getActionSelector('delete'), () => {\n            this.deleteOverride();\n        });\n    }\n\n    /**\n     * Delete a override.\n     *\n     */\n    deleteOverride() {\n        // Check if the recalculation checkbox is checked.\n        const recalculate = this.recalculationCheckbox.prop('checked');\n\n        // Redirect to the delete URL.\n        const targetUrl = new URL(`${Config.wwwroot}/mod/assign/overridedelete.php`);\n        targetUrl.searchParams.append('id', this.getOverrideId());\n        targetUrl.searchParams.append('sesskey', this.getSessionKey());\n        targetUrl.searchParams.append('confirm', 1);\n\n        if (recalculate) {\n            targetUrl.searchParams.append('recalculate', 1);\n        }\n\n        window.location.href = targetUrl.href;\n    }\n}\n"], "names": ["SELECTORS", "OverrideDeleteModal", "Modal", "configure", "modalConfig", "large", "show", "removeOnClose", "setOverrideId", "overrideId", "setSessionKey", "<PERSON><PERSON><PERSON>", "constructor", "root", "recalculationCheckbox", "this", "getModal", "find", "id", "getOverrideId", "key", "getSessionKey", "registerEventListeners", "registerCloseOnCancel", "on", "CustomEvents", "events", "activate", "getActionSelector", "deleteOverride", "recalculate", "prop", "targetUrl", "URL", "Config", "wwwroot", "searchParams", "append", "window", "location", "href"], "mappings": "u/CA2BMA,gCAEqB,8BAMNC,4BAA4BC,eAS7CC,UAAUC,aAENA,YAAYC,OAAQ,EAGpBD,YAAYE,MAAO,EACnBF,YAAYG,eAAgB,QAGtBJ,UAAUC,kBAEXI,cAAcJ,YAAYK,iBAC1BC,cAAcN,YAAYO,YASnCC,YAAYC,YACFA,WAGDC,sBAAwBC,KAAKC,WAAWC,KAAKjB,sCAG7CQ,cAAc,WACdE,cAAc,MAQvBF,cAAcU,SACLT,WAAaS,GAQtBC,uBACWJ,KAAKN,WAQhBC,cAAcU,UACLT,WAAaS,IAQtBC,uBACWN,KAAKJ,WAOhBW,+BAEUA,uBAAuBP,WAGxBQ,6BAGAP,WAAWQ,GAAGC,aAAaC,OAAOC,SAAUZ,KAAKa,kBAAkB,WAAW,UAC1EC,oBAQbA,uBAEUC,YAAcf,KAAKD,sBAAsBiB,KAAK,WAG9CC,UAAY,IAAIC,cAAOC,gBAAOC,2CACpCH,UAAUI,aAAaC,OAAO,KAAMtB,KAAKI,iBACzCa,UAAUI,aAAaC,OAAO,UAAWtB,KAAKM,iBAC9CW,UAAUI,aAAaC,OAAO,UAAW,GAErCP,aACAE,UAAUI,aAAaC,OAAO,cAAe,GAGjDC,OAAOC,SAASC,KAAOR,UAAUQ,kEAhHpBvC,2BACH,oDADGA,+BAEC"}