define("mod_assign/override_form",["exports","jquery","core_form/changechecker"],(function(_exports,_jquery,FormChangeChecker){var obj;
/**
   * A javascript module to enhance the override form.
   *
   * @copyright  2019 <PERSON> <<EMAIL>>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_jquery=(obj=_jquery)&&obj.__esModule?obj:{default:obj},FormChangeChecker=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(FormChangeChecker);_exports.init=(formId,selectElementName)=>{const form=document.getElementById(formId),selectElement=form.querySelector('[name="'.concat(selectElementName,'"]'));(0,_jquery.default)(selectElement).on("change",(()=>{const inputElement=document.createElement("input");inputElement.setAttribute("type","hidden"),inputElement.setAttribute("name","userchange"),inputElement.setAttribute("value",!0),form.appendChild(inputElement),FormChangeChecker.markFormSubmitted(inputElement),form.submit()}))}}));

//# sourceMappingURL=override_form.min.js.map