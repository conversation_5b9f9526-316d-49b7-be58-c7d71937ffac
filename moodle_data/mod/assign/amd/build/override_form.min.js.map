{"version": 3, "file": "override_form.min.js", "sources": ["../src/override_form.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * A javascript module to enhance the override form.\n *\n * @copyright  2019 Ryan <PERSON>yllie <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport $ from 'jquery';\nimport * as FormChangeChecker from 'core_form/changechecker';\n\nexport const init = (formId, selectElementName) => {\n    const form = document.getElementById(formId);\n    const selectElement = form.querySelector(`[name=\"${selectElementName}\"]`);\n\n    $(selectElement).on('change', () => {\n        const inputElement = document.createElement('input');\n        inputElement.setAttribute('type', 'hidden');\n        inputElement.setAttribute('name', 'userchange');\n        inputElement.setAttribute('value', true);\n\n        form.appendChild(inputElement);\n\n        FormChangeChecker.markFormSubmitted(inputElement);\n\n        form.submit();\n    });\n};\n"], "names": ["formId", "selectElementName", "form", "document", "getElementById", "selectElement", "querySelector", "on", "inputElement", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "FormChangeChecker", "markFormSubmitted", "submit"], "mappings": ";;;;;;6lCAyBoB,CAACA,OAAQC,2BACnBC,KAAOC,SAASC,eAAeJ,QAC/BK,cAAgBH,KAAKI,+BAAwBL,6CAEjDI,eAAeE,GAAG,UAAU,WACpBC,aAAeL,SAASM,cAAc,SAC5CD,aAAaE,aAAa,OAAQ,UAClCF,aAAaE,aAAa,OAAQ,cAClCF,aAAaE,aAAa,SAAS,GAEnCR,KAAKS,YAAYH,cAEjBI,kBAAkBC,kBAAkBL,cAEpCN,KAAKY"}