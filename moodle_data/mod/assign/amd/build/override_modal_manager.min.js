define("mod_assign/override_modal_manager",["exports","core/str","mod_assign/override_delete_modal"],(function(_exports,_str,_override_delete_modal){var obj;
/**
   * Modal manager for the override delete modal.
   *
   * @module     mod_assign/override_modal_manager
   * @copyright  2025 Catalyst IT Australia Pty Ltd
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_override_delete_modal=(obj=_override_delete_modal)&&obj.__esModule?obj:{default:obj};const SELECTORS_DELETE_BUTTONS=".delete-override",SELECTORS_PARENT_CONTAINER="#assignoverrides",SELECTORS_USER_GROUP_NAME=".usergroupname";_exports.init=(mode,showRecalculationCheckBox)=>{document.querySelector(SELECTORS_PARENT_CONTAINER).addEventListener("click",(async event=>{const button=event.target.closest(SELECTORS_DELETE_BUTTONS);if(!button)return;event.preventDefault();const name=event.target.closest("tr").querySelector(SELECTORS_USER_GROUP_NAME).innerText,confirmMessage=await getConfirmMessage(mode,name);_override_delete_modal.default.create({templateContext:{confirmmessage:confirmMessage,showpenaltyrecalculation:showRecalculationCheckBox},overrideId:button.getAttribute("data-overrideid"),sessionKey:button.getAttribute("data-sesskey")})}))};const getConfirmMessage=(mode,name)=>{switch(mode){case"group":return(0,_str.get_string)("overridedeletegroupsure","assign",name);case"user":return(0,_str.get_string)("overridedeleteusersure","assign",name);default:return""}}}));

//# sourceMappingURL=override_modal_manager.min.js.map