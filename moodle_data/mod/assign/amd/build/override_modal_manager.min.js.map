{"version": 3, "file": "override_modal_manager.min.js", "sources": ["../src/override_modal_manager.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Modal manager for the override delete modal.\n *\n * @module     mod_assign/override_modal_manager\n * @copyright  2025 Catalyst IT Australia Pty Ltd\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {get_string as getString} from 'core/str';\nimport OverrideDeleteModal from 'mod_assign/override_delete_modal';\n\nconst SELECTORS = {\n    DELETE_BUTTONS: '.delete-override',\n    PARENT_CONTAINER: '#assignoverrides',\n    USER_GROUP_NAME: '.usergroupname',\n};\n\n/**\n * Initialise the modal manager.\n *\n * @param {string} mode The override mode.\n * @param {boolean} showRecalculationCheckBox Whether to show the recalculation checkbox.\n */\nexport const init = (mode, showRecalculationCheckBox) => {\n    document.querySelector(SELECTORS.PARENT_CONTAINER).addEventListener('click', async(event) => {\n        const button = event.target.closest(SELECTORS.DELETE_BUTTONS);\n\n        if (!button) {\n            return;\n        }\n\n        event.preventDefault();\n\n        // Get the name of the user or group from the first column of the row.\n        const name = event.target.closest('tr').querySelector(SELECTORS.USER_GROUP_NAME).innerText;\n\n        // Get the confirm message for the modal.\n        const confirmMessage = await getConfirmMessage(mode, name);\n\n        // Create and show the modal.\n        OverrideDeleteModal.create({\n            templateContext: {\n                confirmmessage: confirmMessage,\n                showpenaltyrecalculation: showRecalculationCheckBox,\n            },\n            overrideId: button.getAttribute('data-overrideid'),\n            sessionKey: button.getAttribute('data-sesskey'),\n        });\n    });\n};\n\n/**\n * Get the confirm message for the modal.\n *\n * @param {string} mode The override mode.\n * @param {boolean} name The name of the user or group.\n * @returns {Promise<string>} The confirm message.\n */\nconst getConfirmMessage = (mode, name) => {\n    switch (mode) {\n        case \"group\":\n            return getString('overridedeletegroupsure', 'assign', name);\n        case \"user\":\n            return getString('overridedeleteusersure', 'assign', name);\n        default:\n            return \"\";\n    }\n};\n"], "names": ["SELECTORS", "mode", "showRecalculationCheckBox", "document", "querySelector", "addEventListener", "async", "button", "event", "target", "closest", "preventDefault", "name", "innerText", "confirmMessage", "getConfirmMessage", "create", "templateContext", "confirmmessage", "showpenaltyrecalculation", "overrideId", "getAttribute", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;8KA0BMA,yBACc,mBADdA,2BAEgB,mBAFhBA,0BAGe,+BASD,CAACC,KAAMC,6BACvBC,SAASC,cAAcJ,4BAA4BK,iBAAiB,SAASC,MAAAA,cACnEC,OAASC,MAAMC,OAAOC,QAAQV,8BAE/BO,cAILC,MAAMG,uBAGAC,KAAOJ,MAAMC,OAAOC,QAAQ,MAAMN,cAAcJ,2BAA2Ba,UAG3EC,qBAAuBC,kBAAkBd,KAAMW,qCAGjCI,OAAO,CACvBC,gBAAiB,CACbC,eAAgBJ,eAChBK,yBAA0BjB,2BAE9BkB,WAAYb,OAAOc,aAAa,mBAChCC,WAAYf,OAAOc,aAAa,4BAYtCN,kBAAoB,CAACd,KAAMW,eACrBX,UACC,eACM,mBAAU,0BAA2B,SAAUW,UACrD,cACM,mBAAU,yBAA0B,SAAUA,oBAE9C"}