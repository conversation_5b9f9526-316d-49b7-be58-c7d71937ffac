{"version": 3, "file": "participant_selector.min.js", "sources": ["../src/participant_selector.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Custom auto-complete adapter to load users from the assignment list_participants webservice.\n *\n * @module     mod_assign/participant_selector\n * @copyright  2015 Damyon Wiese <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\ndefine(['core/ajax', 'jquery', 'core/templates'], function(ajax, $, templates) {\n\n\n    return /** @alias module:mod_assign/participants_selector */ {\n\n        // Public variables and functions.\n        /**\n         * Process the results returned from transport (convert to value + label)\n         *\n         * @method processResults\n         * @param {String} selector\n         * @param {Array} data\n         * @return {Array}\n         */\n        processResults: function(selector, data) {\n            return data;\n        },\n\n        /**\n         * Fetch results based on the current query. This also renders each result from a template before returning them.\n         *\n         * @method transport\n         * @param {String} selector Selector for the original select element\n         * @param {String} query Current search string\n         * @param {Function} success Success handler\n         * @param {Function} failure Failure handler\n         */\n        transport: function(selector, query, success, failure) {\n            var assignmentid = $(selector).attr('data-assignmentid');\n            var groupid = $(selector).attr('data-groupid');\n            var filters = $('[data-region=\"configure-filters\"] input[type=\"checkbox\"]');\n            var filterstrings = [];\n\n            filters.each(function(index, element) {\n                filterstrings[$(element).attr('name')] = $(element).prop('checked');\n            });\n\n            ajax.call([{\n                methodname: 'mod_assign_list_participants',\n                args: {\n                    assignid: assignmentid,\n                    groupid: groupid,\n                    filter: query,\n                    limit: 30,\n                    includeenrolments: false,\n                    tablesort: true\n                }\n            }])[0].then(function(results) {\n                var promises = [];\n                var identityfields = $('[data-showuseridentity]').data('showuseridentity').split(',');\n\n                // We got the results, now we loop over them and render each one from a template.\n                $.each(results, function(index, user) {\n                    var ctx = user,\n                        identity = [],\n                        show = true;\n\n                    if (filterstrings.filter_submitted && !user.submitted) {\n                        show = false;\n                    }\n                    if (filterstrings.filter_notsubmitted && user.submitted) {\n                        show = false;\n                    }\n                    if (filterstrings.filter_requiregrading && !user.requiregrading) {\n                        show = false;\n                    }\n                    if (filterstrings.filter_grantedextension && !user.grantedextension) {\n                        show = false;\n                    }\n                    if (show) {\n                        $.each(identityfields, function(i, k) {\n                            if (typeof user[k] !== 'undefined' && user[k] !== '') {\n                                ctx.hasidentity = true;\n                                identity.push(user[k]);\n                            }\n                        });\n                        ctx.identity = identity.join(', ');\n                        promises.push(templates.render('mod_assign/list_participant_user_summary', ctx).then(function(html) {\n                            return {value: user.id, label: html};\n                        }));\n                    }\n                });\n                // Do the dance for $.when()\n                return $.when.apply($, promises);\n            }).then(function() {\n                var users = [];\n\n                // Determine if we've been passed any arguments..\n                if (arguments[0]) {\n                    // Undo the $.when() dance from arguments object into an array..\n                    users = Array.prototype.slice.call(arguments);\n                }\n\n                success(users);\n                return;\n            }).catch(failure);\n        }\n    };\n});\n"], "names": ["define", "ajax", "$", "templates", "processResults", "selector", "data", "transport", "query", "success", "failure", "assignmentid", "attr", "groupid", "filters", "filterstrings", "each", "index", "element", "prop", "call", "methodname", "args", "assignid", "filter", "limit", "includeenrolments", "tablesort", "then", "results", "promises", "identityfields", "split", "user", "ctx", "identity", "show", "filter_submitted", "submitted", "filter_notsubmitted", "filter_requiregrading", "requiregrading", "filter_grantedextension", "grantedextension", "i", "k", "hasidentity", "push", "join", "render", "html", "value", "id", "label", "when", "apply", "users", "arguments", "Array", "prototype", "slice", "catch"], "mappings": ";;;;;;;AAsBAA,yCAAO,CAAC,YAAa,SAAU,mBAAmB,SAASC,KAAMC,EAAGC,iBAGH,CAWzDC,eAAgB,SAASC,SAAUC,aACxBA,MAYXC,UAAW,SAASF,SAAUG,MAAOC,QAASC,aACtCC,aAAeT,EAAEG,UAAUO,KAAK,qBAChCC,QAAUX,EAAEG,UAAUO,KAAK,gBAC3BE,QAAUZ,EAAE,4DACZa,cAAgB,GAEpBD,QAAQE,MAAK,SAASC,MAAOC,SACzBH,cAAcb,EAAEgB,SAASN,KAAK,SAAWV,EAAEgB,SAASC,KAAK,cAG7DlB,KAAKmB,KAAK,CAAC,CACPC,WAAY,+BACZC,KAAM,CACFC,SAAUZ,aACVE,QAASA,QACTW,OAAQhB,MACRiB,MAAO,GACPC,mBAAmB,EACnBC,WAAW,MAEf,GAAGC,MAAK,SAASC,aACbC,SAAW,GACXC,eAAiB7B,EAAE,2BAA2BI,KAAK,oBAAoB0B,MAAM,YAGjF9B,EAAEc,KAAKa,SAAS,SAASZ,MAAOgB,UACxBC,IAAMD,KACNE,SAAW,GACXC,MAAO,EAEPrB,cAAcsB,mBAAqBJ,KAAKK,YACxCF,MAAO,GAEPrB,cAAcwB,qBAAuBN,KAAKK,YAC1CF,MAAO,GAEPrB,cAAcyB,wBAA0BP,KAAKQ,iBAC7CL,MAAO,GAEPrB,cAAc2B,0BAA4BT,KAAKU,mBAC/CP,MAAO,GAEPA,OACAlC,EAAEc,KAAKe,gBAAgB,SAASa,EAAGC,QACR,IAAZZ,KAAKY,IAAkC,KAAZZ,KAAKY,KACvCX,IAAIY,aAAc,EAClBX,SAASY,KAAKd,KAAKY,QAG3BX,IAAIC,SAAWA,SAASa,KAAK,MAC7BlB,SAASiB,KAAK5C,UAAU8C,OAAO,2CAA4Cf,KAAKN,MAAK,SAASsB,YACnF,CAACC,MAAOlB,KAAKmB,GAAIC,MAAOH,cAKpChD,EAAEoD,KAAKC,MAAMrD,EAAG4B,aACxBF,MAAK,eACA4B,MAAQ,GAGRC,UAAU,KAEVD,MAAQE,MAAMC,UAAUC,MAAMxC,KAAKqC,YAGvChD,QAAQ+C,UAETK,MAAMnD"}