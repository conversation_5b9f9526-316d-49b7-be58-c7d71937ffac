define("mod_assign/quick_grading",["exports","core_form/submit"],(function(_exports,formSubmit){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,formSubmit=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}
/**
   * Module for the quick grading functionality on the submissions page.
   *
   * @module     mod_assign/quick_grading
   * @copyright  2024 Mihail Geshoski <<EMAIL>>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */(formSubmit);const Selectors_quickGradingSaveRegion='[data-region="quick-grading-save"]',Selectors_notifyStudentsCheckbox='input[type="checkbox"][name="sendstudentnotifications"]',Selectors_notifyStudentsHidden='input[type="hidden"][name="sendstudentnotifications"]',Selectors_saveButton='button[type="submit"]';_exports.init=()=>{const quickGradingSaveRegion=document.querySelector(Selectors_quickGradingSaveRegion);if(quickGradingSaveRegion){const quickGradingSaveButton=quickGradingSaveRegion.querySelector(Selectors_saveButton);formSubmit.init(quickGradingSaveButton),quickGradingSaveRegion.addEventListener("change",(e=>{const notifyStudentsCheckbox=e.target.closest(Selectors_notifyStudentsCheckbox);if(notifyStudentsCheckbox){notifyStudentsCheckbox.parentNode.querySelector(Selectors_notifyStudentsHidden).disabled=notifyStudentsCheckbox.checked}}))}}}));

//# sourceMappingURL=quick_grading.min.js.map