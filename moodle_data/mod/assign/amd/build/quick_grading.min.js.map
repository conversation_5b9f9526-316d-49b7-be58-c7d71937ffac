{"version": 3, "file": "quick_grading.min.js", "sources": ["../src/quick_grading.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport * as formSubmit from 'core_form/submit';\n\n/**\n * Module for the quick grading functionality on the submissions page.\n *\n * @module     mod_assign/quick_grading\n * @copyright  2024 <PERSON>hai<PERSON> <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n/** @constant {Object} The object containing the relevant selectors. */\nconst Selectors = {\n    quickGradingSaveRegion: '[data-region=\"quick-grading-save\"]',\n    notifyStudentsCheckbox: 'input[type=\"checkbox\"][name=\"sendstudentnotifications\"]',\n    notifyStudentsHidden: 'input[type=\"hidden\"][name=\"sendstudentnotifications\"]',\n    saveButton: 'button[type=\"submit\"]'\n};\n\n/**\n * Initialize module.\n */\nexport const init = () => {\n    const quickGradingSaveRegion = document.querySelector(Selectors.quickGradingSaveRegion);\n    if (quickGradingSaveRegion) {\n        const quickGradingSaveButton = quickGradingSaveRegion.querySelector(Selectors.saveButton);\n        // Initialize the submit button.\n        formSubmit.init(quickGradingSaveButton);\n        // Add 'change' event listener to the quick grading save region.\n        quickGradingSaveRegion.addEventListener('change', e => {\n            const notifyStudentsCheckbox = e.target.closest(Selectors.notifyStudentsCheckbox);\n            // The target is the 'Notify student' checkbox.\n            if (notifyStudentsCheckbox) {\n                // The 'Notify student' option uses a hidden input and a checkbox. The hidden input is used to submit '0'\n                // as a workaround when the checkbox is unchecked since unchecked checkboxes are not submitted with the\n                // form. Therefore, we need to enable or disable the hidden input based on the checkbox state.\n                const notifyStudentsHidden = notifyStudentsCheckbox.parentNode.querySelector(Selectors.notifyStudentsHidden);\n                notifyStudentsHidden.disabled = notifyStudentsCheckbox.checked;\n            }\n        });\n    }\n};\n"], "names": ["Selectors", "quickGradingSaveRegion", "document", "querySelector", "quickGradingSaveButton", "formSubmit", "init", "addEventListener", "e", "notifyStudentsCheckbox", "target", "closest", "parentNode", "disabled", "checked"], "mappings": ";;;;;;;wBA0BMA,iCACsB,qCADtBA,iCAEsB,0DAFtBA,+BAGoB,wDAHpBA,qBAIU,sCAMI,WACVC,uBAAyBC,SAASC,cAAcH,qCAClDC,uBAAwB,OAClBG,uBAAyBH,uBAAuBE,cAAcH,sBAEpEK,WAAWC,KAAKF,wBAEhBH,uBAAuBM,iBAAiB,UAAUC,UACxCC,uBAAyBD,EAAEE,OAAOC,QAAQX,qCAE5CS,uBAAwB,CAIKA,uBAAuBG,WAAWT,cAAcH,gCACxDa,SAAWJ,uBAAuBK"}