define("mod_assign/repository",["exports","core/ajax"],(function(_exports,_ajax){var obj;
/**
   * A repo for the search partial in the submissions page.
   *
   * @module    mod_assign/repository
   * @copyright 2024 Ilya Tregubov <<EMAIL>>
   * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.userFetch=void 0,_ajax=(obj=_ajax)&&obj.__esModule?obj:{default:obj};_exports.userFetch=(assignid,groupid)=>{const request={methodname:"mod_assign_list_participants",args:{assignid:assignid,groupid:groupid,filter:""}};return _ajax.default.call([request])[0]}}));

//# sourceMappingURL=repository.min.js.map