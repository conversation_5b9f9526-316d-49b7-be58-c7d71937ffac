{"version": 3, "file": "repository.min.js", "sources": ["../src/repository.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * A repo for the search partial in the submissions page.\n *\n * @module    mod_assign/repository\n * @copyright 2024 Ilya Tregubov <<EMAIL>>\n * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport ajax from 'core/ajax';\n\n/**\n * Given a course ID, we want to fetch the learners within this assignment.\n *\n * @method userFetch\n * @param {int} assignid ID of the assignment.\n * @param {int} groupid ID of the selected group.\n * @return {object} jQuery promise\n */\nexport const userFetch = (assignid, groupid) => {\n    const request = {\n        methodname: 'mod_assign_list_participants',\n        args: {\n            assignid: assignid,\n            groupid: groupid,\n            filter: '',\n        },\n    };\n    return ajax.call([request])[0];\n};\n"], "names": ["assignid", "groupid", "request", "methodname", "args", "filter", "ajax", "call"], "mappings": ";;;;;;;8JAiCyB,CAACA,SAAUC,iBAC1BC,QAAU,CACZC,WAAY,+BACZC,KAAM,CACFJ,SAAUA,SACVC,QAASA,QACTI,OAAQ,YAGTC,cAAKC,KAAK,CAACL,UAAU"}