define("mod_assign/timer",["exports","core/notification","core/str"],(function(_exports,_notification,_str){var obj;
/**
   * A javascript module for the time in the assign module.
   *
   * @copyright  2020 <PERSON> <<EMAIL>>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_notification=(obj=_notification)&&obj.__esModule?obj:{default:obj};let endTime=0,timeoutId=null,timer=null;const update=()=>{const now=(new Date).getTime(),secondsLeft=Math.floor((endTime-now)/1e3);if(secondsLeft<=0)return timer.classList.add("alert","alert-danger"),timer.innerHTML="00:00:00",document.getElementById("mod_assign_timelimit_block")&&(0,_str.getString)("caneditsubmission","mod_assign").then((message=>_notification.default.addNotification({message:message}))).catch(_notification.default.exception),void(timeoutId&&clearTimeout(timeoutId));var secs;secondsLeft<300?(timer.classList.remove("alert-warning"),timer.classList.add("alert","alert-danger")):secondsLeft<900&&(timer.classList.remove("alert-danger"),timer.classList.add("alert","alert-warning")),timer.innerHTML=(secs=secondsLeft,[Math.floor(secs/3600),Math.floor(secs/60)%60,secs%60].filter(((value,index)=>0!==value||index>0)).map((value=>"".concat(value).padStart(2,"0"))).join(":")),timeoutId=setTimeout(update,500)};_exports.init=timerId=>{timer=document.getElementById(timerId),endTime=M.pageloadstarttime.getTime()+1e3*timer.dataset.starttime,update()}}));

//# sourceMappingURL=timer.min.js.map