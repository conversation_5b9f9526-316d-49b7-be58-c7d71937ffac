{"version": 3, "file": "timer.min.js", "sources": ["../src/timer.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport Notification from 'core/notification';\nimport {getString} from 'core/str';\n\n/**\n * A javascript module for the time in the assign module.\n *\n * @copyright  2020 Matt <PERSON> <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n/**\n * Timestamp at which time runs out.\n *\n * @property {Number} endTime\n */\nlet endTime = 0;\n\n/**\n * ID of the timeout that updates the clock.\n *\n * @property {Number} timeoutId\n */\nlet timeoutId = null;\n\n/**\n * The timer element.\n *\n * @property {Element} timer\n */\nlet timer = null;\n\n/**\n * Helper method to convert time remaining in seconds into HH:MM:SS format.\n *\n * @method formatSeconds\n * @param {Number} secs Time remaining in seconds to get value for.\n * @return {String} Time remaining in HH:MM:SS format.\n */\nconst formatSeconds = (secs) => {\n    const hours = Math.floor(secs / 3600);\n    const minutes = Math.floor(secs / 60) % 60;\n    const seconds = secs % 60;\n\n    return [hours, minutes, seconds]\n        // Remove the hours column if there is less than 1 hour left.\n        .filter((value, index) => value !== 0 || index > 0)\n        // Ensure that all fields are two digit numbers.\n        .map(value => `${value}`.padStart(2, '0'))\n        .join(\":\");\n};\n\n/**\n * Stop the timer, if it is running.\n *\n * @method stop\n */\nconst stop = () => {\n    if (timeoutId) {\n        clearTimeout(timeoutId);\n    }\n};\n\n/**\n * Function to update the clock with the current time left.\n *\n * @method update\n */\nconst update = () => {\n    const now = new Date().getTime();\n    const secondsLeft = Math.floor((endTime - now) / 1000);\n\n    // If time has expired, set the hidden form field that says time has expired.\n    if (secondsLeft <= 0) {\n        timer.classList.add('alert', 'alert-danger');\n        timer.innerHTML = '00:00:00';\n\n        // Only add a notification on the assign submission page.\n        if (document.getElementById(\"mod_assign_timelimit_block\")) {\n            getString('caneditsubmission', 'mod_assign')\n                .then(message => Notification.addNotification({message}))\n                .catch(Notification.exception);\n        }\n\n        stop();\n        return;\n    } else if (secondsLeft < 300) { // Add danger style when less than 5 minutes left.\n        timer.classList.remove('alert-warning');\n        timer.classList.add('alert', 'alert-danger');\n    } else if (secondsLeft < 900) { // Add warning style when less than 15 minutes left.\n        timer.classList.remove('alert-danger');\n        timer.classList.add('alert', 'alert-warning');\n    }\n\n    // Update the time display.\n    timer.innerHTML = formatSeconds(secondsLeft);\n\n    // Arrange for this method to be called again soon.\n    timeoutId = setTimeout(update, 500);\n};\n\n/**\n * Set up the submission timer.\n *\n * @method init\n * @param {Number} timerId Unique ID of the timer element.\n */\nexport const init = (timerId) => {\n    timer = document.getElementById(timerId);\n    endTime = M.pageloadstarttime.getTime() + (timer.dataset.starttime * 1000);\n    update();\n};\n"], "names": ["endTime", "timeoutId", "timer", "update", "now", "Date", "getTime", "secondsLeft", "Math", "floor", "classList", "add", "innerHTML", "document", "getElementById", "then", "message", "Notification", "addNotification", "catch", "exception", "clearTimeout", "secs", "remove", "filter", "value", "index", "map", "padStart", "join", "setTimeout", "timerId", "M", "pageloadstarttime", "dataset", "starttime"], "mappings": ";;;;;;0JA8BIA,QAAU,EAOVC,UAAY,KAOZC,MAAQ,WAsCNC,OAAS,WACLC,KAAM,IAAIC,MAAOC,UACjBC,YAAcC,KAAKC,OAAOT,QAAUI,KAAO,QAG7CG,aAAe,SACfL,MAAMQ,UAAUC,IAAI,QAAS,gBAC7BT,MAAMU,UAAY,WAGdC,SAASC,eAAe,kDACd,oBAAqB,cAC1BC,MAAKC,SAAWC,sBAAaC,gBAAgB,CAACF,QAAAA,YAC9CG,MAAMF,sBAAaG,gBAvB5BnB,WACAoB,aAAapB,YApBEqB,IAAAA,KA+CRf,YAAc,KACrBL,MAAMQ,UAAUa,OAAO,iBACvBrB,MAAMQ,UAAUC,IAAI,QAAS,iBACtBJ,YAAc,MACrBL,MAAMQ,UAAUa,OAAO,gBACvBrB,MAAMQ,UAAUC,IAAI,QAAS,kBAIjCT,MAAMU,WAxDaU,KAwDaf,YAnDzB,CAJOC,KAAKC,MAAMa,KAAO,MAChBd,KAAKC,MAAMa,KAAO,IAAM,GACxBA,KAAO,IAIlBE,QAAO,CAACC,MAAOC,QAAoB,IAAVD,OAAeC,MAAQ,IAEhDC,KAAIF,OAAS,UAAGA,OAAQG,SAAS,EAAG,OACpCC,KAAK,MAiDV5B,UAAY6B,WAAW3B,OAAQ,oBASd4B,UACjB7B,MAAQW,SAASC,eAAeiB,SAChC/B,QAAUgC,EAAEC,kBAAkB3B,UAAuC,IAA1BJ,MAAMgC,QAAQC,UACzDhC"}