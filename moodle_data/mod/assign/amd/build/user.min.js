define("mod_assign/user",["exports","core_user/comboboxsearch/user","mod_assign/repository"],(function(_exports,_user,Repository){var obj;function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_user=(obj=_user)&&obj.__esModule?obj:{default:obj},Repository=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(Repository);const selectors_component=".user-search",selectors_groupid='[data-region="groupid"]',selectors_instance='[data-region="instance"]',component=document.querySelector(selectors_component),groupID=parseInt(component.querySelector(selectors_groupid).dataset.groupid,10),assignID=parseInt(component.querySelector(selectors_instance).dataset.instance,10);
/**
   * Allow the user to search for users in the action bar.
   *
   * @module    mod_assign/user
   * @copyright 2024 Ilya Tregubov <<EMAIL>>
   * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */
class User extends _user.default{constructor(baseUrl){super(),this.baseUrl=baseUrl}static init(baseUrl){return new User(baseUrl)}selectAllResultsLink(){const url=new URL(this.baseUrl);return url.searchParams.set("search",this.getSearchTerm()),url.toString()}fetchDataset(){return Repository.userFetch(assignID,groupID).then((r=>r))}selectOneLink(userID){const url=new URL(this.baseUrl);return url.searchParams.set("search",this.getSearchTerm()),url.searchParams.set("userid",userID.toString()),url.toString()}}return _exports.default=User,_exports.default}));

//# sourceMappingURL=user.min.js.map