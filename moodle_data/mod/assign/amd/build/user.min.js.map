{"version": 3, "file": "user.min.js", "sources": ["../src/user.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport UserSearch from 'core_user/comboboxsearch/user';\nimport * as Repository from 'mod_assign/repository';\n\n// Define our standard lookups.\nconst selectors = {\n    component: '.user-search',\n    groupid: '[data-region=\"groupid\"]',\n    instance: '[data-region=\"instance\"]',\n    currentvalue: '[data-region=\"currentvalue\"]',\n};\nconst component = document.querySelector(selectors.component);\nconst groupID = parseInt(component.querySelector(selectors.groupid).dataset.groupid, 10);\nconst assignID = parseInt(component.querySelector(selectors.instance).dataset.instance, 10);\n\n/**\n * Allow the user to search for users in the action bar.\n *\n * @module    mod_assign/user\n * @copyright 2024 Ilya Tregubov <<EMAIL>>\n * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\nexport default class User extends UserSearch {\n\n    /**\n     * Construct the class.\n     *\n     * @param {string} baseUrl The base URL for the page.\n     */\n    constructor(baseUrl) {\n        super();\n        this.baseUrl = baseUrl;\n    }\n\n    /**\n     * Allow the class to be invoked via PHP.\n     *\n     * @param {string} baseUrl The base URL for the page.\n     * @returns {User}\n     */\n    static init(baseUrl) {\n        return new User(baseUrl);\n    }\n\n    /**\n     * Build up the view all link.\n     *\n     * @returns {string|*}\n     */\n    selectAllResultsLink() {\n        const url = new URL(this.baseUrl);\n        url.searchParams.set('search', this.getSearchTerm());\n\n        return url.toString();\n    }\n\n    /**\n     * Get the data we will be searching against in this component.\n     *\n     * @returns {Promise<*>}\n     */\n    fetchDataset() {\n        return Repository.userFetch(assignID, groupID).then((r) => r);\n    }\n\n    /**\n     * Build up the link that is dedicated to a particular result.\n     *\n     * @param {Number} userID The ID of the user selected.\n     * @returns {string|*}\n     */\n    selectOneLink(userID) {\n        const url = new URL(this.baseUrl);\n        url.searchParams.set('search', this.getSearchTerm());\n        url.searchParams.set('userid', userID.toString());\n\n        return url.toString();\n    }\n}\n"], "names": ["selectors", "component", "document", "querySelector", "groupID", "parseInt", "dataset", "groupid", "assignID", "instance", "User", "UserSearch", "constructor", "baseUrl", "selectAllResultsLink", "url", "URL", "this", "searchParams", "set", "getSearchTerm", "toString", "fetchDataset", "Repository", "userFetch", "then", "r", "selectOneLink", "userID"], "mappings": "2sCAmBMA,oBACS,eADTA,kBAEO,0BAFPA,mBAGQ,2BAGRC,UAAYC,SAASC,cAAcH,qBACnCI,QAAUC,SAASJ,UAAUE,cAAcH,mBAAmBM,QAAQC,QAAS,IAC/EC,SAAWH,SAASJ,UAAUE,cAAcH,oBAAoBM,QAAQG,SAAU;;;;;;;;MASnEC,aAAaC,cAO9BC,YAAYC,sBAEHA,QAAUA,oBASPA,gBACD,IAAIH,KAAKG,SAQpBC,6BACUC,IAAM,IAAIC,IAAIC,KAAKJ,gBACzBE,IAAIG,aAAaC,IAAI,SAAUF,KAAKG,iBAE7BL,IAAIM,WAQfC,sBACWC,WAAWC,UAAUhB,SAAUJ,SAASqB,MAAMC,GAAMA,IAS/DC,cAAcC,cACJb,IAAM,IAAIC,IAAIC,KAAKJ,gBACzBE,IAAIG,aAAaC,IAAI,SAAUF,KAAKG,iBACpCL,IAAIG,aAAaC,IAAI,SAAUS,OAAOP,YAE/BN,IAAIM"}