<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Strings for component 'assignfeedback_editpdf', language 'en'
 *
 * @package   assignfeedback_editpdf
 * @copyright 2012 Davo Smith
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['addtoquicklist'] = 'Add to quicklist';
$string['annotationcolour'] = 'Annotation colour';
$string['black'] = 'Black';
$string['blue'] = 'Blue';
$string['cannotopenpdf'] = 'Cannot open the PDF. The file may be corrupt, or in an unsupported format.';
$string['clear'] = 'Clear';
$string['colourpicker'] = 'Colour picker';
$string['commentcolour'] = 'Comment colour';
$string['comment'] = 'Comments';
$string['commentindex'] = 'Index of comments';
$string['commentlabel'] = '{$a->pnum}.{$a->cnum}';
$string['command'] = 'Command:';
$string['commentcontextmenu'] = 'Comment context menu';
$string['couldnotsavepage'] = 'Could not save page {$a}';
$string['currentstamp'] = 'Stamp';
$string['default'] = 'Enabled by default';
$string['default_help'] = 'If set, this feedback method will be enabled by default for all new assignments.';
$string['deleteannotation'] = 'Delete annotation';
$string['deletecomment'] = 'Delete comment';
$string['deletefeedback'] = 'Delete feedback PDF';
$string['downloadablefilename'] = 'feedback.pdf';
$string['downloadfeedback'] = 'Download feedback PDF';
$string['drag'] = 'Drag';
$string['errorgenerateimage'] = 'Error generating image with ghostscript, debugging info: {$a}';
$string['errorpdfpage'] = 'There was an error while generating this page.';
$string['editpdf'] = 'Annotate PDF';
$string['editpdf_help'] = 'Annotate student submissions directly in the browser and produce an edited downloadable PDF.';
$string['enabled'] = 'Annotate PDF';
$string['enabled_help'] = 'If enabled, the teacher will be able to create annotated PDF files when marking assignment submissions. This allows the teacher to add comments, drawing and stamps directly on top of the student\'s work. The annotating is done in the browser and no extra software is required.';
$string['expcolcomments'] = 'Expand/collapse all comments';
$string['filter'] = 'Filter comments...';
$string['generatefeedback'] = 'Generate feedback PDF';
$string['gotopage'] = 'Go to page';
$string['green'] = 'Green';
$string['gsimage'] = 'Ghostscript test image';
$string['pathtogserror'] = 'The configured path to ghostscript is not correctly set: {$a}';
$string['pathtogspathdesc'] = 'Please note that annotate PDF requires the path to ghostscript to be set in {$a}.';
$string['highlight'] = 'Highlight';
$string['jsrequired'] = 'JavaScript is required to annotate a PDF. Please enable JavaScript in your browser to use this feature.';
$string['launcheditor'] = 'Launch PDF editor...';
$string['line'] = 'Line';
$string['loadingeditor'] = 'Loading PDF editor';
$string['navigatenext'] = 'Next page (Alt/Shift-Alt/Ctrl-Option + {$a})';
$string['navigateprevious'] = 'Previous page (Alt/Shift-Alt/Ctrl-Option + {$a})';
$string['oval'] = 'Oval';
$string['output'] = 'Output:';
$string['pagenumber'] = 'Page {$a}';
$string['pagexofy'] = 'Page {$a->page} of {$a->total}';
$string['pen'] = 'Pen';
$string['partialwarning'] = 'Some of the files in this submission can only be accessed by direct download.';
$string['pluginname'] = 'Annotate PDF';
$string['privacy:metadata:colourpurpose'] = 'Colour of the comment or annotation';
$string['privacy:metadata:conversionpurpose'] = 'Files are converted to PDFs to allow for annotations.';
$string['privacy:metadata:filepurpose'] = 'Stores an annotated PDF with feedback for the user.';
$string['privacy:metadata:rawtextpurpose'] = 'Stores raw text for the quick data.';
$string['privacy:metadata:tablepurpose'] = 'Stores teacher specified quicklist comments';
$string['privacy:metadata:userid'] = 'The user ID';
$string['privacy:path'] = 'PDF Feedback';
$string['generatingpdf'] = 'Generating the PDF...';
$string['rectangle'] = 'Rectangle';
$string['red'] = 'Red';
$string['result'] = 'Result:';
$string['searchcomments'] = 'Search comments';
$string['select'] = 'Select';
$string['stamppicker'] = 'Stamp picker';
$string['stampsdesc'] = 'Stamps must be image files (recommended size: 40x40). These images can be used with the stamp tool to annotate the PDF.';
$string['stamps'] = 'Stamps';
$string['stamp'] = 'Stamp';
$string['test_doesnotexist'] = 'The ghostscript path points to a non-existent file';
$string['test_empty'] = 'The ghostscript path is empty - please enter the correct path';
$string['testgs'] = 'Test ghostscript path';
$string['test_isdir'] = 'The ghostscript path points to a folder, please include the ghostscript program in the path you specify';
$string['test_notestfile'] = 'The test PDF is missing';
$string['test_notexecutable'] = 'The ghostscript points to a file that is not executable';
$string['test_ok'] = 'The ghostscript path appears to be OK - please check you can see the message in the image below';
$string['toolbarbutton'] = '{$a->tool} {$a->shortcut}';
$string['tool'] = 'Tool';
$string['viewfeedbackonline'] = 'View annotated PDF...';
$string['white'] = 'White';
$string['yellow'] = 'Yellow';
$string['draftchangessaved'] = 'Draft annotations saved';
$string['preparesubmissionsforannotation'] = 'Prepare submissions for annotation';
$string['rotateleft'] = 'Rotate 90 degrees to the left';
$string['rotateright'] = 'Rotate 90 degrees to the right';
