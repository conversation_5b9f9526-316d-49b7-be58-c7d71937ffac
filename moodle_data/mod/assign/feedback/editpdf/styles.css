.assignfeedback_editpdf_widget .toolbar ul {
    display: none;
}

.assignfeedback_editpdf_widget .toolbar li {
    list-style-type: none;
}

.assignfeedback_editpdf_widget .drawingcanvas {
    position: relative;
    min-width: 817px;
    min-height: 400px;
    background-repeat: no-repeat;
    background-color: #ccc;
    margin-left: auto;
    margin-right: auto;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, .2), 0 1px 20px rgba(0, 0, 0, .2);
}

.assignfeedback_editpdf_widget .moodle-dialogue-bd .drawingregion {
    position: inherit;
}

.assignfeedback_editpdf_widget .drawingregion[data-currenttool=select] .commentdrawable textarea,
.assignfeedback_editpdf_widget .drawingregion[data-currenttool=select] .commentdrawable svg {
    cursor: move;
}

.assignfeedback_editpdf_widget .infoicon {
    display: none;
}

.assignfeedback_editpdf_widget .warningmessages {
    position: absolute;
    margin-left: 20px;
    margin-right: 20px;
    bottom: 20px;
}

.assignfeedback_editpdf_widget .drawingregion {
    border: 1px solid #ccc;
    left: 1em;
    right: 1em;
    top: 52px;
    bottom: 0;
    position: absolute;
    overflow: auto;
    background-color: #ccc;
    touch-action: none;
}

.assignfeedback_editpdf_widget {
    user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
}

.assignfeedback_editpdf_widget .pageheader {
    background-color: #ebebeb;
    border-bottom: 1px solid #ccc;
    padding: 0;
    padding-left: 20px;
    padding-right: 20px;
    min-height: 50px;
    height: 52px;
    overflow: auto;
}

.moodle-dialogue-base .moodle-dialogue.assignfeedback_editpdf_widget .moodle-dialogue-bd {
    padding: 0;
}

.yui3-colourpicker-hidden,
.yui3-commentsearch-hidden,
.yui3-commentmenu-hidden {
    display: none;
}

.assignfeedback_editpdf_widget .pageheader button img {
    padding-top: 3px;
    vertical-align: top;
}

.assignfeedback_editpdf_widget .pageheader button:active {
    background-color: #ccc;
}

.assignfeedback_editpdf_widget .pageheader select,
.assignfeedback_editpdf_widget .pageheader button {
    background: none;
    padding: 4px 7px;
    border: 0;
    border-radius: 0;
    margin: 0;
    height: 30px;
    line-height: 30px;
    vertical-align: top;
    cursor: pointer;
}

.assignfeedback_editpdf_widget .pageheader select {
    vertical-align: top;
    -webkit-appearance: none;
    -moz-appearance: menulist-text;
    background-color: #fff;
    padding: 0 10px;
}

.assignfeedback_editpdf_widget .pageheader select::-ms-expand {
    display: none;
}

.assignfeedback_editpdf_widget .pageheader .navigation button + button,
.assignfeedback_editpdf_widget .pageheader .toolbar button + button,
.assignfeedback_editpdf_widget .pageheader .navigation select + button,
.assignfeedback_editpdf_widget .pageheader .toolbar select + button {
    border-left: 1px solid #ccc;
    border-right: 0;
}

.assignfeedback_editpdf_widget .pageheader .navigation button {
    border-right: 1px solid #ccc;
}

.assignfeedback_editpdf_widget .pageheader .toolbar,
.assignfeedback_editpdf_widget .pageheader .navigation-search,
.assignfeedback_editpdf_widget .pageheader .navigation-expcol,
.assignfeedback_editpdf_widget .pageheader .navigation {
    border: 1px solid #ccc;
    border-bottom-color: #b3b3b3;
    border-radius: 4px;
    margin: 10px 4px;
    background-color: white;
    height: 30px;
    line-height: 30px;
    padding: 0;
}

.assignfeedback_editpdf_commentsearch ul {
    max-height: 400px;
    overflow-y: auto;
    padding: 1em;
}

.assignfeedback_editpdf_commentsearch ul li pre {
    background-color: #efefef;
    white-space: pre-wrap;
    word-break: break-word;
}

.assignfeedback_editpdf_commentsearch ul li pre:hover {
    background-color: #ddd;
}

.assignfeedback_editpdf_commentsearch ul li {
    margin: 2px;
}

.assignfeedback_editpdf_commentsearch a pre {
    font-family: helvetica;
    margin: 0;
    padding: 4px;
}

.assignfeedback_editpdf_widget .navigation-search,
.assignfeedback_editpdf_widget .navigation-expcol,
.assignfeedback_editpdf_widget .navigation {
    float: left;
}

.assignfeedback_editpdf_widget .toolbar_group {
    float: right;
}

.assignfeedback_editpdf_widget .toolbar button {
    box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
}

.assignfeedback_editpdf_widget .toolbar > button.rotateleftbutton {
    /*rtl:raw:
    border-right: 1px solid #ccc;
    border-left: 0;
    */
    /*rtl:ignore*/
    float: left;
}

.assignfeedback_editpdf_widget .toolbar > button.rotaterightbutton {
    /*rtl:ignore*/
    float: right;
}

.assignfeedback_editpdf_widget .toolbar {
    float: left;
}

.assignfeedback_editpdf_widget .navigation,
.assignfeedback_editpdf_widget .navigation-search,
.assignfeedback_editpdf_widget .navigation-expcol,
.assignfeedback_editpdf_widget .toolbar {
    display: inline-block;
}

.assignfeedback_editpdf_colourpicker ul {
    margin: 0;
}

.assignfeedback_editpdf_dropdown li.quicklist_comment {
    width: 200px;
}

.assignfeedback_editpdf_dropdown li.quicklist_comment a {
    white-space: nowrap;
    display: inline-block;
    max-width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.assignfeedback_editpdf_dropdown a.delete_quicklist_comment {
    float: right;
    width: 20px;
}

.assignfeedback_editpdf_dropdown button {
    border: 0;
    background: none;
    padding: 6px 7px;
    border-radius: 0;
    border-top: 1px solid #ccc;
}

.assignfeedback_editpdf_dropdown li:first-child button {
    border-top: 0;
}

.moodle-dialogue-base .moodle-dialogue.assignfeedback_editpdf_dropdown .moodle-dialogue-wrap {
    box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    margin-left: 0;
    margin-right: 0;
    margin-top: 0;
    border-radius: 4px;
}

.moodle-dialogue-base .moodle-dialogue.assignfeedback_editpdf_dropdown .moodle-dialogue-bd {
    padding: 0;
}

.moodle-dialogue-base .assignfeedback_editpdf_dropdown .moodle-dialogue-wrap .moodle-dialogue-hd,
.moodle-dialogue-base .assignfeedback_editpdf_dropdown .moodle-dialogue-wrap .moodle-dialogue-ft {
    display: none;
}

.assignfeedback_editpdf_menu li hr {
    margin: 0;
}

.assignfeedback_editpdf_menu li a {
    text-decoration: none;
    color: #555;
    margin-left: 10px;
    margin-right: 10px;
    margin-top: 4px;
    margin-bottom: 4px;
}

ul.assignfeedback_editpdf_search {
    margin: 0;
}

.assignfeedback_editpdf_search li {
    list-style-type: none;
}

.assignfeedback_editpdf_search li a {
    text-decoration: none;
    color: #555;
}

.assignfeedback_editpdf_menu li:hover {
    background-color: #ebebeb;
}

ul.assignfeedback_editpdf_menu {
    margin: 0;
    max-height: 40vh;
    overflow-y: auto;
    overflow-x: hidden;
}

.assignfeedback_editpdf_menu li {
    list-style-type: none;
    margin: 0;
    border-radius: 4px;
}

.assignfeedback_editpdf_menu li button {
    margin: 0;
    background: none;
}

.assignfeedback_editpdf_widget .pageheader button:hover {
    background-color: #ebebeb;
    background-image: radial-gradient(ellipse at center, #fff 60%, #dfdfdf 100%);
}

.assignfeedback_editpdf_widget .pageheader button.assignfeedback_editpdf_selectedbutton:hover,
.assignfeedback_editpdf_widget .pageheader button.assignfeedback_editpdf_selectedbutton {
    background-color: #dfdfdf;
    background-image: radial-gradient(ellipse at center, #fff 40%, #dfdfdf 100%);
}

.assignfeedback_editpdf_widget .commentdrawable img {
    margin: 0 5px;
}

.assignfeedback_editpdf_widget .commentdrawable a {
    float: right;
    position: relative;
    left: -17px;
    top: 2px;
    height: 16px;
    width: 16px;
}

.assignfeedback_editpdf_widget .commentdrawable textarea {
    padding: 4px;
    padding-right: 20px;
    resize: none;
    overflow: hidden;
    color: black;
    border: 2px solid #ccc;
    border-radius: 4px;
    font-size: 16px;
    font-family: helvetica;
    min-height: 1.2em;
}

.assignfeedback_editpdf_widget .commentdrawable textarea:focus {
    outline: thin dotted #333;
    outline-color: -webkit-focus-ring-color;
    outline-width: 5px;
    outline-style: auto;
    outline-offset: -2px;
}

.assignfeedback_editpdf_widget .commentdrawable {
    display: flex;
    z-index: 52;
    height: 0;
}
.assignfeedback_editpdf_widget .annotation {
    z-index: 51;
}

.assignfeedback_editpdf_widget .deleteannotationbutton {
    z-index: 51;
}

.assignfeedback_editpdf_widget .commentdrawable label {
    display: inline-block;
}

.assignfeedback_editpdf_widget .commentdrawable svg {
    display: none;
}

.assignfeedback_editpdf_widget .commentdrawable.commentcollapsed {
    z-index: auto;
    width: 24px;
}

.assignfeedback_editpdf_widget .commentdrawable.commentcollapsed textarea,
.assignfeedback_editpdf_widget .commentdrawable.commentcollapsed a {
    display: none;
}

.assignfeedback_editpdf_widget .commentdrawable.commentcollapsed svg {
    display: inline-block;
    width: 24px;
    height: 24px;
}

.assignfeedback_editpdf_widget .drawingcanvas .loading .progressbarlabel {
    text-align: center;
}

.hideoverflow {
    overflow: hidden;
    position: relative;
}

@media (max-width: 960px) {
    .assignfeedback_editpdf_widget .pageheader {
        height: 104px;
    }

    .assignfeedback_editpdf_widget .drawingregion {
        top: 104px;
    }
}

@media (max-width: 767px) {
    .assignfeedback_editpdf_widget .drawingregion {
        position: relative;
        margin-bottom: 1em;
        top: 0;
        max-height: 312px;
    }

    .assignfeedback_editpdf_widget .pageheader {
        height: auto;
    }
}

@media (max-width: 480px) {
    .assignfeedback_editpdf_widget .pageheader {
        padding-left: 5px;
        padding-right: 5px;
    }
}
