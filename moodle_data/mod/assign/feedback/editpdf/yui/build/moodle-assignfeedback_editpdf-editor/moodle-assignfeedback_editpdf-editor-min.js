YUI.add("moodle-assignfeedback_editpdf-editor",function(u,e){var c,i,s,n,a,h,l,p,f,_,m,b,w,o=M.cfg.wwwroot+"/mod/assign/feedback/editpdf/ajax.php",y=M.cfg.wwwroot+"/mod/assign/feedback/editpdf/ajax_progress.php",k="assignfeedback_editpdf_widget",v=".navigate-previous-button",x=" .navigate-next-button",A=".searchcommentsbutton",S=".expcolcommentsbutton",N=".assignfeedback_editpdf_commentsearch input",D=".assignfeedback_editpdf_commentsearch ul",T=".navigate-page-select",I=".loading",C=".progress-info.progress-striped",r=".drawingregion",g=".drawingcanvas",q=".commentcolourbutton",E=".annotationcolourbutton",Y=".warningmessages",z=".infoicon",R='input[name="assignfeedback_editpdf_haschanges"]',X=".currentstampbutton",L='[data-region="user-info"]',P=".rotateleftbutton",j=".rotaterightbutton",O={white:"rgb(255,255,255)",yellow:"rgb(255,236,174)",red:"rgb(249,181,179)",green:"rgb(214,234,178)",blue:"rgb(203,217,237)",clear:"rgba(255,255,255, 0)"},d={white:"rgb(255,255,255)",yellow:"rgb(255,207,53)",red:"rgb(239,69,64)",green:"rgb(152,202,62)",blue:"rgb(125,159,211)",black:"rgb(51,51,51)"},B={comment:".commentbutton",pen:".penbutton",line:".linebutton",rectangle:".rectanglebutton",oval:".ovalbutton",stamp:".stampbutton",select:".selectbutton",drag:".dragbutton",highlight:".highlightbutton"},t=function(t,e){this.x=parseInt(t,10),this.y=parseInt(e,10),this.clip=function(t){return this.x<t.x&&(this.x=t.x),this.x>t.x+t.width&&(this.x=t.x+t.width),this.y<t.y&&(this.y=t.y),this.y>t.y+t.height&&(this.y=t.y+t.height),this}};M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.point=t,t=function(t,e,i,s){this.x=t,this.y=e,this.width=i,this.height=s,this.bound=function(t){for(var e,i=0,s=0,n=0,a=0,o=0,o=0;o<t.length;o++)((e=t[o]).x<i||0===o)&&(i=e.x),(e.x>s||0===o)&&(s=e.x),(e.y<n||0===o)&&(n=e.y),(e.y>a||0===o)&&(a=e.y);return this.x=i,this.y=n,this.width=s-i,this.height=a-n,this},this.has_min_width=function(){return 5<=this.width},this.has_min_height=function(){return 5<=this.height},this.set_min_width=function(){this.width=5},this.set_min_height=function(){this.height=5}},M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.rect=t,t=function(){this.start=!1,this.end=!1,this.starttime=0,this.annotationstart=!1,this.tool="drag",this.commentcolour="yellow",this.annotationcolour="red",this.stamp="",this.path=[]},M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.edit=t,t=function(t){this.editor=t,this.shapes=[],this.nodes=[],this.erase=function(){if(this.shapes)for(;0<this.shapes.length;)this.editor.graphic.removeShape(this.shapes.pop());if(this.nodes)for(;0<this.nodes.length;)this.nodes.pop().remove()},this.scroll_update=function(t,e){for(var i,s,n=0;n<this.nodes.length;n++)i=this.nodes[n].getData("x"),s=this.nodes[n].getData("y"),i!==undefined&&s!==undefined&&(this.nodes[n].setX(parseInt(i,10)-t),this.nodes[n].setY(parseInt(s,10)-e))},this.store_position=function(t,e,i){var s=this.editor.get_dialogue_element(r),n=parseInt(s.get("scrollLeft"),10),s=parseInt(s.get("scrollTop"),10);t.setData("x",e+n),t.setData("y",i+s)}},M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.drawable=t,(c=function(t){c.superclass.constructor.apply(this,[t])}).NAME="annotation",c.ATTRS={},u.extend(c,u.Base,{editor:null,gradeid:0,pageno:0,x:0,y:0,endx:0,endy:0,path:"",type:"rect",colour:"red",drawable:!1,initializer:function(t){this.editor=t.editor||null,this.gradeid=parseInt(t.gradeid,10)||0,this.pageno=parseInt(t.pageno,10)||0,this.x=parseInt(t.x,10)||0,this.y=parseInt(t.y,10)||0,this.endx=parseInt(t.endx,10)||0,this.endy=parseInt(t.endy,10)||0,this.path=t.path||"",this.type=t.type||"rect",this.colour=t.colour||"red",this.drawable=!1},clean:function(){return{gradeid:this.gradeid,x:parseInt(this.x,10),y:parseInt(this.y,10),endx:parseInt(this.endx,10),endy:parseInt(this.endy,10),type:this.type,path:this.path,pageno:this.pageno,colour:this.colour}},draw_highlight:function(){var t,e,i,s=this.editor.get_dialogue_element(r),n=this.editor.get_dialogue_element(g).getXY();return this.editor.currentannotation===this&&((t=new M.assignfeedback_editpdf.rect).bound([new M.assignfeedback_editpdf.point(this.x,this.y),new M.assignfeedback_editpdf.point(this.endx,this.endy)]),e=this.editor.graphic.addShape({type:u.Rect,width:t.width,height:t.height,stroke:{weight:4,color:"rgba(200, 200, 255, 0.9)"},fill:{color:"rgba(200, 200, 255, 0.5)"},x:t.x,y:t.y}),this.drawable.shapes.push(e),e=u.Node.create('<img src="'+M.util.image_url("trash","assignfeedback_editpdf")+'"/>'),i=u.Node.create('<a href="#" role="button"></a>'),e.setAttrs({alt:M.util.get_string("deleteannotation","assignfeedback_editpdf")}),e.setStyles({backgroundColor:"white"}),i.addClass("deleteannotationbutton"),i.append(e),s.append(i),i.setData("annotation",this),i.on("click",this.remove,this),i.on("key",this.remove,"space,enter",this),i.setX(n[0]+t.x+t.width-18),i.setY(n[1]+t.y+6),this.drawable.nodes.push(i)),this.drawable},draw:function(){return this.draw_highlight(),this.drawable},remove:function(t){var e,i;for(t.preventDefault(),e=this.editor.pages[this.editor.currentpage].annotations,i=0;i<e.length;i++)if(e[i]===this)return e.splice(i,1),this.drawable&&this.drawable.erase(),this.editor.currentannotation=!1,void this.editor.save_current_page()},move:function(t,e){var i,s,n,a=t-this.x,o=e-this.y;this.x+=a,this.y+=o,this.endx+=a,this.endy+=o,this.path&&(i=[],t=this.path.split(":"),u.each(t,function(t){n=t.split(","),s=parseInt(n[0],10),n=parseInt(n[1],10),i.push(s+a+","+(n+o))}),this.path=i.join(":")),this.drawable&&this.drawable.erase(),this.editor.drawables.push(this.draw())},draw_current_edit:function(t){return t&&!1},init_from_edit:function(t){var e=new M.assignfeedback_editpdf.rect;return e.bound([t.start,t.end]),this.gradeid=this.editor.get("gradeid"),this.pageno=this.editor.currentpage,this.x=e.x,this.y=e.y,this.endx=e.x+e.width,this.endy=e.y+e.height,
this.colour=t.annotationcolour,this.path="",e.has_min_width()&&e.has_min_height()}}),M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.annotation=c,(i=function(t){i.superclass.constructor.apply(this,[t])}).NAME="annotationline",i.ATTRS={},u.extend(i,M.assignfeedback_editpdf.annotation,{draw:function(){var t=new M.assignfeedback_editpdf.drawable(this.editor),e=this.editor.graphic.addShape({type:u.Path,fill:!1,stroke:{weight:4,color:d[this.colour]}});return e.moveTo(this.x,this.y),e.lineTo(this.endx,this.endy),e.end(),t.shapes.push(e),this.drawable=t,i.superclass.draw.apply(this)},draw_current_edit:function(t){var e=new M.assignfeedback_editpdf.drawable(this.editor),i=this.editor.graphic.addShape({type:u.Path,fill:!1,stroke:{weight:4,color:d[t.annotationcolour]}});return i.moveTo(t.start.x,t.start.y),i.lineTo(t.end.x,t.end.y),i.end(),e.shapes.push(i),e},init_from_edit:function(t){return this.gradeid=this.editor.get("gradeid"),this.pageno=this.editor.currentpage,this.x=t.start.x,this.y=t.start.y,this.endx=t.end.x,this.endy=t.end.y,this.colour=t.annotationcolour,this.path="",!(this.endx-this.x==0&&this.endy-this.y==0)}}),M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.annotationline=i,(s=function(t){s.superclass.constructor.apply(this,[t])}).NAME="annotationrectangle",s.ATTRS={},u.extend(s,M.assignfeedback_editpdf.annotation,{draw:function(){var t=new M.assignfeedback_editpdf.drawable(this.editor),e=new M.assignfeedback_editpdf.rect;return e.bound([new M.assignfeedback_editpdf.point(this.x,this.y),new M.assignfeedback_editpdf.point(this.endx,this.endy)]),e=this.editor.graphic.addShape({type:u.Rect,width:e.width,height:e.height,stroke:{weight:4,color:d[this.colour]},x:e.x,y:e.y}),t.shapes.push(e),this.drawable=t,s.superclass.draw.apply(this)},draw_current_edit:function(t){var e=new M.assignfeedback_editpdf.drawable(this.editor),i=new M.assignfeedback_editpdf.rect;return i.bound([new M.assignfeedback_editpdf.point(t.start.x,t.start.y),new M.assignfeedback_editpdf.point(t.end.x,t.end.y)]),i.has_min_width()||i.set_min_width(),i.has_min_height()||i.set_min_height(),t=this.editor.graphic.addShape({type:u.Rect,width:i.width,height:i.height,stroke:{weight:4,color:d[t.annotationcolour]},x:i.x,y:i.y}),e.shapes.push(t),e}}),M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.annotationrectangle=s,(n=function(t){n.superclass.constructor.apply(this,[t])}).NAME="annotationoval",n.ATTRS={},u.extend(n,M.assignfeedback_editpdf.annotation,{draw:function(){var t=new M.assignfeedback_editpdf.drawable(this.editor),e=new M.assignfeedback_editpdf.rect;return e.bound([new M.assignfeedback_editpdf.point(this.x,this.y),new M.assignfeedback_editpdf.point(this.endx,this.endy)]),e=this.editor.graphic.addShape({type:u.Ellipse,width:e.width,height:e.height,stroke:{weight:4,color:d[this.colour]},x:e.x,y:e.y}),t.shapes.push(e),this.drawable=t,n.superclass.draw.apply(this)},draw_current_edit:function(t){var e=new M.assignfeedback_editpdf.drawable(this.editor),i=new M.assignfeedback_editpdf.rect;return i.bound([new M.assignfeedback_editpdf.point(t.start.x,t.start.y),new M.assignfeedback_editpdf.point(t.end.x,t.end.y)]),i.has_min_width()||i.set_min_width(),i.has_min_height()||i.set_min_height(),t=this.editor.graphic.addShape({type:u.Ellipse,width:i.width,height:i.height,stroke:{weight:4,color:d[t.annotationcolour]},x:i.x,y:i.y}),e.shapes.push(t),e}}),M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.annotationoval=n,(a=function(t){a.superclass.constructor.apply(this,[t])}).NAME="annotationpen",a.ATTRS={},u.extend(a,M.assignfeedback_editpdf.annotation,{draw:function(){var e,t=new M.assignfeedback_editpdf.drawable(this.editor),i=this.editor.graphic.addShape({type:u.Path,fill:!1,stroke:{weight:4,color:d[this.colour]}}),s=!0,n=this.path.split(":");return u.each(n,function(t){e=t.split(","),s?(i.moveTo(e[0],e[1]),s=!1):i.lineTo(e[0],e[1])},this),i.end(),t.shapes.push(i),this.drawable=t,a.superclass.draw.apply(this)},draw_current_edit:function(t){var e=new M.assignfeedback_editpdf.drawable(this.editor),i=this.editor.graphic.addShape({type:u.Path,fill:!1,stroke:{weight:4,color:d[t.annotationcolour]}}),s=!0;return u.each(t.path,function(t){s?(i.moveTo(t.x,t.y),s=!1):i.lineTo(t.x,t.y)},this),i.end(),e.shapes.push(i),e},init_from_edit:function(t){var e=new M.assignfeedback_editpdf.rect,i=[],s=0;for(e.bound(t.path),s=0;s<t.path.length;s++)i.push(parseInt(t.path[s].x,10)+","+parseInt(t.path[s].y,10));return this.gradeid=this.editor.get("gradeid"),this.pageno=this.editor.currentpage,this.x=e.x,this.y=e.y,this.endx=e.x+e.width,this.endy=e.y+e.height,this.colour=t.annotationcolour,this.path=i.join(":"),e.has_min_width()||e.has_min_height()}}),M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.annotationpen=a,(h=function(t){h.superclass.constructor.apply(this,[t])}).NAME="annotationhighlight",h.ATTRS={},u.extend(h,M.assignfeedback_editpdf.annotation,{draw:function(){var t,e=new M.assignfeedback_editpdf.drawable(this.editor),i=new M.assignfeedback_editpdf.rect;return i.bound([new M.assignfeedback_editpdf.point(this.x,this.y),new M.assignfeedback_editpdf.point(this.endx,this.endy)]),t=(t=(t=d[this.colour]).replace("rgb","rgba")).replace(")",",0.5)"),t=this.editor.graphic.addShape({type:u.Rect,width:i.width,height:i.height,stroke:!1,fill:{color:t},x:i.x,y:i.y}),e.shapes.push(t),this.drawable=e,h.superclass.draw.apply(this)},draw_current_edit:function(t){var e,i=new M.assignfeedback_editpdf.drawable(this.editor),s=new M.assignfeedback_editpdf.rect;return s.bound([new M.assignfeedback_editpdf.point(t.start.x,t.start.y),new M.assignfeedback_editpdf.point(t.end.x,t.end.y)]),s.has_min_width()||s.set_min_width(),e=(e=(e=d[t.annotationcolour]).replace("rgb","rgba")).replace(")",",0.5)"),e=this.editor.graphic.addShape({type:u.Rect,width:s.width,height:20,stroke:!1,fill:{color:e},x:s.x,y:t.start.y-10}),
i.shapes.push(e),i},init_from_edit:function(t){var e=new M.assignfeedback_editpdf.rect;return e.bound([t.start,t.end]),this.gradeid=this.editor.get("gradeid"),this.pageno=this.editor.currentpage,this.x=e.x,this.y=t.start.y-10,this.endx=e.x+e.width,this.endy=t.start.y+10,this.colour=t.annotationcolour,this.page="",e.has_min_width()}}),M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.annotationhighlight=h,(l=function(t){l.superclass.constructor.apply(this,[t])}).NAME="annotationstamp",l.ATTRS={},u.extend(l,M.assignfeedback_editpdf.annotation,{draw:function(){var t=new M.assignfeedback_editpdf.drawable(this.editor),e=this.editor.get_dialogue_element(g),i=this.editor.get_window_coordinates(new M.assignfeedback_editpdf.point(this.x,this.y)),s=u.Node.create("<div/>");return s.addClass("annotation"),s.addClass("stamp"),s.setStyles({position:"absolute",display:"inline-block",backgroundImage:"url("+this.editor.get_stamp_image_url(this.path)+")",width:this.endx-this.x,height:this.endy-this.y,backgroundSize:"100% 100%"}),e.append(s),s.setX(i.x),s.setY(i.y),t.store_position(s,i.x,i.y),this.editor.get("readonly")||(s.on("gesturemovestart",this.editor.edit_start,null,this.editor),s.on("gesturemove",this.editor.edit_move,null,this.editor),s.on("gesturemoveend",this.editor.edit_end,null,this.editor)),t.nodes.push(s),this.drawable=t,l.superclass.draw.apply(this)},draw_current_edit:function(t){var e,i,s=new M.assignfeedback_editpdf.rect,n=new M.assignfeedback_editpdf.drawable(this.editor),a=this.editor.get_dialogue_element(r);return s.bound([t.start,t.end]),i=this.editor.get_window_coordinates(new M.assignfeedback_editpdf.point(s.x,s.y)),(e=u.Node.create("<div/>")).addClass("annotation"),e.addClass("stamp"),e.setStyles({position:"absolute",display:"inline-block",backgroundImage:"url("+this.editor.get_stamp_image_url(t.stamp)+")",width:s.width,height:s.height,backgroundSize:"100% 100%"}),a.append(e),e.setX(i.x),e.setY(i.y),n.store_position(e,i.x,i.y),n.nodes.push(e),n},init_from_edit:function(t){var e=new M.assignfeedback_editpdf.rect;return e.bound([t.start,t.end]),e.width<40&&(e.width=40),e.height<40&&(e.height=40),this.gradeid=this.editor.get("gradeid"),this.pageno=this.editor.currentpage,this.x=e.x,this.y=e.y,this.endx=e.x+e.width,this.endy=e.y+e.height,this.colour=t.annotationcolour,this.path=t.stamp,!0},move:function(t,e){t-=this.x,e-=this.y;this.x+=t,this.y+=e,this.endx+=t,this.endy+=e,this.drawable&&this.drawable.erase(),this.editor.drawables.push(this.draw())}}),M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.annotationstamp=l,u.extend(p=function(t){t.draggable=!1,t.centered=!1,t.width="auto",t.visible=!1,t.footerContent="",p.superclass.constructor.apply(this,[t])},M.core.dialogue,{initializer:function(t){var e,i;p.superclass.initializer.call(this,t),this.get("boundingBox").addClass("assignfeedback_editpdf_dropdown"),e=this.get("buttonNode"),t=this.bodyNode,(i=u.Node.create("<h3/>")).addClass("accesshide"),i.setHTML(this.get("headerText")),t.prepend(i),t.on("clickoutside",function(t){this.get("visible")&&t.target.get("id")!==e.get("id")&&t.target.ancestor().get("id")!==e.get("id")&&(t.preventDefault(),this.hide())},this),e.on("click",function(t){t.preventDefault(),this.show()},this),e.on("key",this.show,"enter,space",this)},show:function(){var t=this.get("buttonNode"),e=p.superclass.show.call(this);return this.align(t,[u.WidgetPositionAlign.TL,u.WidgetPositionAlign.BL]),e}},{NAME:"Dropdown menu",ATTRS:{headerText:{value:""},buttonNode:{value:null}}}),u.Base.modifyAttrs(p,{modal:{getter:function(){return!1}}}),M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.dropdown=p,u.extend(f=function(t){f.superclass.constructor.apply(this,[t])},M.assignfeedback_editpdf.dropdown,{initializer:function(t){var e,n=u.Node.create('<ul role="menu" class="assignfeedback_editpdf_menu"/>');u.each(this.get("colours"),function(t,e){var i=M.util.get_string(e,"assignfeedback_editpdf"),s=this.get("iconprefix")+e,s=M.util.image_url(s,"assignfeedback_editpdf"),i=u.Node.create('<button><img alt="'+i+'" src="'+s+'"/></button>');i.setAttribute("data-colour",e),i.setAttribute("data-rgb",t),i.setAttribute("role","menuitem"),i.setStyle("backgroundImage","none"),(s=u.Node.create("<li/>")).append(i),s.setAttribute("role","none"),n.append(s)},this),e=u.Node.create("<div/>"),n.delegate("click",this.callback_handler,"button",this),n.delegate("key",this.callback_handler,"down:13","button",this),this.set("headerText",M.util.get_string("colourpicker","assignfeedback_editpdf")),e.append(n),this.set("bodyContent",e),f.superclass.initializer.call(this,t)},callback_handler:function(t){t.preventDefault();var e=this.get("callback"),i=this.get("context");this.hide(),u.bind(e,i,t)()}},{NAME:"Colourpicker",ATTRS:{colours:{value:{}},callback:{value:null},context:{value:null},iconprefix:{value:"colour_"}}}),M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.colourpicker=f,u.extend(_=function(t){_.superclass.constructor.apply(this,[t])},M.assignfeedback_editpdf.dropdown,{initializer:function(t){var i=u.Node.create('<ul role="menu" class="assignfeedback_editpdf_menu"/>');u.each(this.get("stamps"),function(t){var e=M.util.get_string("stamp","assignfeedback_editpdf"),e=u.Node.create('<button><img height="16" width="16" alt="'+e+'" src="'+t+'"/></button>');e.setAttribute("data-stamp",t),e.setAttribute("role","menuitem"),e.setStyle("backgroundImage","none"),(t=u.Node.create("<li/>")).append(e),t.setAttribute("role","none"),i.append(t)},this),i.delegate("click",this.callback_handler,"button",this),i.delegate("key",this.callback_handler,"down:13","button",this),this.set("headerText",M.util.get_string("stamppicker","assignfeedback_editpdf")),this.set("bodyContent",i),_.superclass.initializer.call(this,t)},callback_handler:function(t){t.preventDefault();var e=this.get("callback"),i=this.get("context");this.hide(),u.bind(e,i,t)()}},{
NAME:"Colourpicker",ATTRS:{stamps:{value:[]},callback:{value:null},context:{value:null}}}),M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.stamppicker=_,u.extend(m=function(t){m.superclass.constructor.apply(this,[t])},M.assignfeedback_editpdf.dropdown,{initializer:function(t){var e=this.get("comment"),i=u.Node.create('<ul role="menu" class="assignfeedback_editpdf_menu"/>'),s=u.Node.create('<li><a tabindex="-1" href="#">'+M.util.get_string("addtoquicklist","assignfeedback_editpdf")+"</a></li>");s.on("click",e.add_to_quicklist,e),s.on("key",e.add_to_quicklist,"enter,space",e),i.append(s),(s=u.Node.create('<li><a tabindex="-1" href="#">'+M.util.get_string("deletecomment","assignfeedback_editpdf")+"</a></li>")).on("click",function(t){t.preventDefault(),this.menu.hide(),this.remove()},e),s.on("key",function(){e.menu.hide(),e.remove()},"enter,space",e),i.append(s),s=u.Node.create("<li><hr/></li>"),i.append(s),this.set("headerText",M.util.get_string("commentcontextmenu","assignfeedback_editpdf")),(s=u.Node.create("<div/>")).append(i),this.set("bodyContent",s),m.superclass.initializer.call(this,t)},show:function(){var n,a=this.get("boundingBox").one("ul");a.all(".quicklist_comment").remove(!0),(n=this.get("comment")).deleteme=!1,u.each(n.editor.quicklist.comments,function(t){var e=u.Node.create('<li class="quicklist_comment"></li>'),i=u.Node.create('<a href="#" tabindex="-1">'+t.rawtext+"</a>"),s=u.Node.create('<a href="#" tabindex="-1" class="delete_quicklist_comment"><img class="icon" src="'+M.util.image_url("t/delete","core")+'" alt="'+M.util.get_string("deletecomment","assignfeedback_editpdf")+'"/></a>');i.setAttribute("title",t.rawtext),e.append(i),e.append(s),a.append(e),e.on("click",n.set_from_quick_comment,n,t),e.on("key",n.set_from_quick_comment,"space,enter",n,t),s.on("click",n.remove_from_quicklist,n,t),s.on("key",n.remove_from_quicklist,"space,enter",n,t)},this),m.superclass.show.call(this)}},{NAME:"Commentmenu",ATTRS:{comment:{value:null}}}),M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.commentmenu=m,u.extend(b=function(t){t.draggable=!1,t.centered=!0,t.width="400px",t.visible=!1,t.headerContent=M.util.get_string("searchcomments","assignfeedback_editpdf"),t.footerContent="",b.superclass.constructor.apply(this,[t])},M.core.dialogue,{initializer:function(){var t,e,i;this.get("boundingBox").addClass("assignfeedback_editpdf_commentsearch"),this.get("editor"),t=u.Node.create("<div/>"),e=M.util.get_string("filter","assignfeedback_editpdf"),e=u.Node.create('<input type="text" size="20" placeholder="'+e+'"/>'),t.append(e),i=u.Node.create('<ul role="menu" class="assignfeedback_editpdf_search"/>'),t.append(i),e.on("keyup",this.filter_search_comments,this),i.delegate("click",this.focus_on_comment,"a",this),i.delegate("key",this.focus_on_comment,"enter,space","a",this),this.set("bodyContent",t)},filter_search_comments:function(){var t=this.get("id"),e=u.one("#"+t+N),t=u.one("#"+t+D),i=e.get("value");t.all("li").each(function(t){-1!==t.get("text").indexOf(i)?t.show():t.hide()})},focus_on_comment:function(t){t.preventDefault();var t=t.target.ancestor("li").getData("comment"),e=this.get("editor");this.hide(),t.pageno=t.clean().pageno,t.pageno!==e.currentpage&&(e.currentpage=t.pageno,e.change_page()),t.node=t.drawable.nodes[0].one("textarea"),t.node.ancestor("div").removeClass("commentcollapsed"),t.node.focus()},show:function(){var i=this.get("boundingBox").one("ul"),t=this.get("editor");i.all("li").remove(!0),u.each(t.pages,function(t){u.each(t.comments,function(t){var e=u.Node.create('<li><a href="#" tabindex="-1"><pre>'+t.rawtext+"</pre></a></li>");i.append(e),e.setData("comment",t)},this)},this),this.centerDialogue(),b.superclass.show.call(this)}},{NAME:"commentsearch",ATTRS:{editor:{value:null}}}),u.Base.modifyAttrs(b,{modal:{getter:function(){return!0}}}),M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.commentsearch=b,t=function(d,t,e,i,s,n,a,o){this.editor=d,this.gradeid=t||0,this.x=parseInt(i,10)||0,this.y=parseInt(s,10)||0,this.width=parseInt(n,10)||0,this.rawtext=o||"",this.pageno=e||0,this.colour=a||"yellow",this.drawable=!1,this.deleteme=!1,this.menulink=null,this.menu=null,this.clean=function(){return{gradeid:this.gradeid,x:parseInt(this.x,10),y:parseInt(this.y,10),width:parseInt(this.width,10),rawtext:this.rawtext,pageno:parseInt(this.pageno,10),colour:this.colour}},this.draw=function(t){var e=new M.assignfeedback_editpdf.drawable(this.editor),i=this.editor.get_dialogue_element(g),s=u.Node.create("<textarea/>"),n=u.Node.create('<div class="commentdrawable"/>'),a=u.Node.create("<label/>"),o=u.Node.create('<svg xmlns="http://www.w3.org/2000/svg" viewBox="-0.5 -0.5 13 13" preserveAspectRatio="xMinYMin meet"><path d="M11 0H1C.4 0 0 .4 0 1v6c0 .6.4 1 1 1h1v4l4-4h5c.6 0 1-.4 1-1V1c0-.6-.4-1-1-1z" fill="currentColor" opacity="0.9" stroke="rgb(153, 153, 153)" stroke-width="0.5"/></svg>'),r=u.Node.create('<a href="#"><img class="icon" src="'+M.util.image_url("t/contextmenu","core")+'"/></a>');return this.menulink=r,n.append(a),a.append(s),n.append(o),n.setAttribute("tabindex","-1"),a.setAttribute("tabindex","0"),s.setAttribute("tabindex","-1"),r.setAttribute("tabindex","0"),this.editor.get("readonly")?s.setAttribute("readonly","readonly"):n.append(r),this.width<100&&(this.width=100),a=this.editor.get_window_coordinates(new M.assignfeedback_editpdf.point(this.x,this.y)),s.setStyles({width:this.width+"px",backgroundColor:O[this.colour],color:"rgb(51, 51, 51)"}),i.append(n),n.setStyle("position","absolute"),n.setX(a.x),n.setY(a.y),e.store_position(n,a.x,a.y),e.nodes.push(n),s.set("value",this.rawtext),i=s.get("scrollHeight"),s.setStyles({height:i+"px",overflow:"hidden"}),o.setStyle("color",O[this.colour]),this.attach_events(s,r),t?s.focus():d.collapsecomments&&n.addClass("commentcollapsed"),this.drawable=e},this.delete_comment_later=function(){this.deleteme&&!this.is_menu_active()&&this.remove()},
this.is_menu_active=function(){return null!==this.menu&&this.menu.get("visible")},this.attach_events=function(i,e){var s=i.ancestor("div"),t=i.ancestor("label"),n=t.next("svg");i.collapse=function(t){i.collapse.delay=u.later(t,i,function(){d.collapsecomments&&!this.is_menu_active()&&s.addClass("commentcollapsed")}.bind(this))}.bind(this),i.expand=function(){!0!==i.getData("dragging")&&(i.collapse.delay&&i.collapse.delay.cancel(),s.removeClass("commentcollapsed"))},s.on("mouseenter",function(){"comment"!==d.currentedit.tool&&"select"!==d.currentedit.tool&&!this.editor.get("readonly")||i.expand()},this),s.on("click|tap",function(){i.expand(),i.focus()},this),i.on("keyup",function(t){9===t.keyCode&&t.shiftKey&&"0"===e.getAttribute("tabindex")&&e.focus(),e.setAttribute("tabindex","0")},this),e.on("keydown",function(t){9===t.keyCode&&t.shiftKey&&e.setAttribute("tabindex","-1")},this),t.on("focus",function(){i.active=!0,i.collapse.delay&&i.collapse.delay.cancel(),i.setAttribute("tabindex","0"),i.expand(),i.focus(),t.setAttribute("tabindex","-1")},this),e.on("focus",function(){i.active=!0,i.collapse.delay&&i.collapse.delay.cancel(),this.deleteme=!1,t.setAttribute("tabindex","0")},this),i.on("blur",function(){i.setAttribute("tabindex","-1")},this),t.on("blur",function(){t.setAttribute("tabindex","0")},this),s.on("mouseleave",function(){d.collapsecomments&&!0!==i.active&&i.collapse(400)},this),s.on("blur",function(){i.active=!1,i.collapse(800)},this),this.editor.get("readonly")||(i.on("blur",function(){this.rawtext=i.get("value"),this.width=parseInt(i.getStyle("width"),10),""===this.rawtext.replace(/^\s+|\s+$/g,"")&&(this.deleteme=!0,u.later(400,this,this.delete_comment_later)),this.editor.save_current_page(),this.editor.editingcomment=!1},this),e.setData("comment",this),i.on("keyup",function(){i.setStyle("height","auto");var t=i.get("scrollHeight");t===parseInt(i.getStyle("height"),10)+8&&(t-=8),i.setStyle("height",t+"px")}),i.on("gesturemovestart",function(t){"select"===d.currentedit.tool&&(t.preventDefault(),d.collapsecomments?(i.setData("offsetx",8),i.setData("offsety",8)):(i.setData("offsetx",t.clientX-s.getX()),i.setData("offsety",t.clientY-s.getY())))}),i.on("gesturemove",function(t){var e;"select"===d.currentedit.tool&&(e=t.clientX-i.getData("offsetx"),t=t.clientY-i.getData("offsety"),!0!==i.getData("dragging")&&(i.collapse(0),i.setData("dragging",!0)),e=this.editor.get_canvas_coordinates(new M.assignfeedback_editpdf.point(e,t)),(t=this.editor.get_canvas_bounds(!0)).x=0,t.y=0,t.width-=24,t.height-=24,e.clip(t),this.x=e.x,this.y=e.y,t=this.editor.get_window_coordinates(e),s.setX(t.x),s.setY(t.y),this.drawable.store_position(s,t.x,t.y))},null,this),i.on("gesturemoveend",function(){"select"===d.currentedit.tool&&(!0===i.getData("dragging")&&i.setData("dragging",!1),this.editor.save_current_page())},null,this),n.on("gesturemovestart",function(t){"select"===d.currentedit.tool&&(t.preventDefault(),i.setData("offsetx",t.clientX-s.getX()),i.setData("offsety",t.clientY-s.getY()),i.expand())}),n.on("gesturemove",function(t){var e;"select"===d.currentedit.tool&&(e=t.clientX-i.getData("offsetx"),t=t.clientY-i.getData("offsety"),!0!==i.getData("dragging")&&(i.collapse(100),i.setData("dragging",!0)),e=this.editor.get_canvas_coordinates(new M.assignfeedback_editpdf.point(e,t)),(t=this.editor.get_canvas_bounds(!0)).x=0,t.y=0,t.width-=24,t.height-=24,e.clip(t),this.x=e.x,this.y=e.y,t=this.editor.get_window_coordinates(e),s.setX(t.x),s.setY(t.y),this.drawable.store_position(s,t.x,t.y))},null,this),n.on("gesturemoveend",function(){"select"===d.currentedit.tool&&(!0===i.getData("dragging")&&i.setData("dragging",!1),this.editor.save_current_page())},null,this),this.menu=new M.assignfeedback_editpdf.commentmenu({buttonNode:this.menulink,comment:this}))},this.remove=function(){for(var t=0,e=this.editor.pages[this.editor.currentpage].comments,t=0;t<e.length;t++)if(e[t]===this)return e.splice(t,1),this.drawable.erase(),void this.editor.save_current_page()},this.remove_from_quicklist=function(t,e){t.preventDefault(),t.stopPropagation(),this.menu.hide(),this.editor.quicklist.remove(e)},this.set_from_quick_comment=function(t,e){t.preventDefault(),this.menu.hide(),this.deleteme=!1,this.rawtext=e.rawtext,this.width=e.width,this.colour=e.colour,this.editor.save_current_page(),this.editor.redraw(),this.node=this.drawable.nodes[0].one("textarea"),this.node.ancestor("div").removeClass("commentcollapsed"),this.node.focus()},this.add_to_quicklist=function(t){t.preventDefault(),this.menu.hide(),this.editor.quicklist.add(this)},this.draw_current_edit=function(t){var e=new M.assignfeedback_editpdf.drawable(this.editor),i=new M.assignfeedback_editpdf.rect;return i.bound([t.start,t.end]),t=this.editor.graphic.addShape({type:u.Rect,width:i.width,height:i.height,fill:{color:O[t.commentcolour]},x:i.x,y:i.y}),e.shapes.push(t),e},this.init_from_edit=function(t){var e=new M.assignfeedback_editpdf.rect;return e.bound([t.start,t.end]),e.width<100&&(e.width=100),this.gradeid=this.editor.get("gradeid"),this.pageno=this.editor.currentpage,this.x=e.x,this.y=e.y,this.width=e.width,this.colour=t.commentcolour,this.rawtext="",e.has_min_width()&&e.has_min_height()},this.updatePosition=function(){var t=this.drawable.nodes[0].one("textarea"),t=t.ancestor("div"),e=new M.assignfeedback_editpdf.point(this.x,this.y),e=this.editor.get_window_coordinates(e);t.setX(e.x),t.setY(e.y),this.drawable.store_position(t,e.x,e.y)}},M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.comment=t,t=function(t,e,i,s){this.rawtext=e||"",this.id=t||0,this.width=i||100,this.colour=s||"yellow"},M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.quickcomment=t,t=function(t){this.editor=t,this.comments=[],this.add=function(t){var e=o;""!==t.rawtext&&(t={method:"post",context:this,sync:!1,data:{sesskey:M.cfg.sesskey,action:"addtoquicklist",userid:this.editor.get("userid"),commenttext:t.rawtext,width:t.width,
colour:t.colour,attemptnumber:this.editor.get("attemptnumber"),assignmentid:this.editor.get("assignmentid")},on:{success:function(t,e){var i,s;try{if((i=u.JSON.parse(e.responseText)).error)return new M.core.ajaxException(i);s=new M.assignfeedback_editpdf.quickcomment(i.id,i.rawtext,i.width,i.colour),this.comments.push(s),this.comments.sort(function(t,e){return t.rawtext.localeCompare(e.rawtext)})}catch(n){return new M.core.exception(n)}},failure:function(t,e){return M.core.exception(e.responseText)}}},u.io(e,t))},this.remove=function(e){var t,i=o;e&&(t={method:"post",context:this,sync:!1,data:{sesskey:M.cfg.sesskey,action:"removefromquicklist",userid:this.editor.get("userid"),commentid:e.id,attemptnumber:this.editor.get("attemptnumber"),assignmentid:this.editor.get("assignmentid")},on:{success:function(){var t=this.comments.indexOf(e);0<=t&&this.comments.splice(t,1)},failure:function(t,e){return M.core.exception(e.responseText)}}},u.io(i,t))},this.load=function(){var t=o,e={method:"get",context:this,sync:!1,data:{sesskey:M.cfg.sesskey,action:"loadquicklist",userid:this.editor.get("userid"),attemptnumber:this.editor.get("attemptnumber"),assignmentid:this.editor.get("assignmentid")},on:{success:function(t,e){var i;try{if((i=u.JSON.parse(e.responseText)).error)return new M.core.ajaxException(i);u.each(i,function(t){t=new M.assignfeedback_editpdf.quickcomment(t.id,t.rawtext,t.width,t.colour);this.comments.push(t)},this),this.comments.sort(function(t,e){return t.rawtext.localeCompare(e.rawtext)})}catch(s){return new M.core.exception(s)}},failure:function(t,e){return M.core.exception(e.responseText)}}};u.io(t,e)}},M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.quickcommentlist=t,(w=function(){w.superclass.constructor.apply(this,arguments)}).prototype={oldannotationcoordinates:null,dialogue:null,panel:null,pagecount:0,currentpage:0,pages:[],documentstatus:0,loadingicon:null,pageimage:null,graphic:null,currentedit:new M.assignfeedback_editpdf.edit,currentdrawable:!1,drawables:[],currentcomment:null,currentannotation:null,lastannotation:null,lastannotationtool:"pen",quicklist:null,searchcommentswindow:null,currentstamp:null,stamps:[],editingcomment:!1,collapsecomments:!0,initializer:function(){var e=u.one("#"+this.get("linkid"));e&&(e.on("click",this.link_handler,this),e.on("key",this.link_handler,"down:13",this),require(["mod_assign/grading_review_panel"],function(t){t=new t,t=t.getReviewPanel("assignfeedback_editpdf");t&&((t=u.one(t)).empty(),e.ancestor(".fitem").hide(),this.open_in_panel(t)),this.currentedit.start=!1,this.currentedit.end=!1,this.get("readonly")||(this.quicklist=new M.assignfeedback_editpdf.quickcommentlist(this))}.bind(this)))},refresh_button_state:function(){var t,e,i=this.get_dialogue_element(q),s=M.util.image_url("background_colour_"+this.currentedit.commentcolour,"assignfeedback_editpdf");switch(i.one("img").setAttribute("src",s),"clear"===this.currentedit.commentcolour?i.one("img").setStyle("borderStyle","dashed"):i.one("img").setStyle("borderStyle","solid"),i=this.get_dialogue_element(E),s=M.util.image_url("colour_"+this.currentedit.annotationcolour,"assignfeedback_editpdf"),i.one("img").setAttribute("src",s),(s=this.get_dialogue_element(B[this.currentedit.tool])).addClass("assignfeedback_editpdf_selectedbutton"),s.setAttribute("aria-pressed","true"),this.get_dialogue_element(r).setAttribute("data-currenttool",this.currentedit.tool),i=this.get_dialogue_element(X),t=this.get_stamp_image_url(this.currentedit.stamp),i.one("img").setAttrs({src:t,height:"16",width:"16"}),e=this.get_dialogue_element(g),this.currentedit.tool){case"drag":e.setStyle("cursor","move");break;case"highlight":e.setStyle("cursor","text");break;case"select":e.setStyle("cursor","default");break;case"stamp":e.setStyle("cursor","url("+t+"), crosshair");break;default:e.setStyle("cursor","crosshair")}},get_canvas_bounds:function(){var t=this.get_dialogue_element(g),e=t.getXY(),i=e[0],e=e[1],s=parseInt(t.getStyle("width"),10),t=parseInt(t.getStyle("height"),10);return new M.assignfeedback_editpdf.rect(i,e,s,t)},get_canvas_coordinates:function(t){var e=this.get_canvas_bounds(),t=new M.assignfeedback_editpdf.point(t.x-e.x,t.y-e.y);return e.x=e.y=0,t.clip(e),t},get_window_coordinates:function(t){var e=this.get_canvas_bounds();return new M.assignfeedback_editpdf.point(t.x+e.x,t.y+e.y)},open_in_panel:function(t){(this.panel=t).append(this.get("body")),t.addClass(k),this.loadingicon=this.get_dialogue_element(I),t=this.get_dialogue_element(g),this.graphic=new u.Graphic({render:t}),this.get("readonly")||(t.on("gesturemovestart",this.edit_start,null,this),t.on("gesturemove",this.edit_move,null,this),t.on("gesturemoveend",this.edit_end,null,this),this.refresh_button_state()),this.start_generation()},link_handler:function(t){var e=!0;t.preventDefault(),this.dialogue||(this.dialogue=new M.core.dialogue({headerContent:this.get("header"),bodyContent:this.get("body"),footerContent:this.get("footer"),modal:!0,width:"840px",visible:!1,draggable:!0}),this.dialogue.get("boundingBox").addClass(k),this.loadingicon=this.get_dialogue_element(I),t=this.get_dialogue_element(g),this.graphic=new u.Graphic({render:t}),this.get("readonly")||(t.on("gesturemovestart",this.edit_start,null,this),t.on("gesturemove",this.edit_move,null,this),t.on("gesturemoveend",this.edit_end,null,this),this.refresh_button_state()),this.start_generation(),t.on("windowresize",this.resize,this),e=!1),this.dialogue.centerDialogue(),this.dialogue.show(),this.dialogue.dd.on("drag:end",this.redraw,this),e&&this.resize()},start_generation:function(){this.poll_document_conversion_status()},poll_document_conversion_status:function(){var n=this.get("userid");u.io(o,{method:"get",context:this,sync:!1,data:{sesskey:M.cfg.sesskey,action:"pollconversions",userid:this.get("userid"),attemptnumber:this.get("attemptnumber"),assignmentid:this.get("assignmentid"),readonly:this.get("readonly")?1:0},on:{success:function(t,e){var i,s=u.one(L);s&&(
i=s.getAttribute("data-userid"))&&i!=n||(s=!1,(i=this.handle_response_data(e))&&(!0!==this.get("readonly")?(this.documentstatus=i.status,0===i.status||1===i.status||3===i.status?s=!0:2!==i.status&&-1!==i.status||(i.pageready==i.pagecount?this.prepare_pages_for_display(i):(this.update_page_load_progress(),this.start_document_to_image_conversion())),s&&u.later(1e3,this,this.poll_document_conversion_status)):this.prepare_pages_for_display(i)))},failure:function(t,e){return new M.core.exception(e.responseText)}}})},start_document_to_image_conversion:function(){u.io(o,{method:"get",context:this,sync:!1,data:{sesskey:M.cfg.sesskey,action:"pollconversions",userid:this.get("userid"),attemptnumber:this.get("attemptnumber"),assignmentid:this.get("assignmentid"),readonly:this.get("readonly")?1:0},on:{success:function(t,e){e=this.handle_response_data(e);e&&(this.documentstatus=e.status,2===e.status&&this.prepare_pages_for_display(e))},failure:function(t,e){return new M.core.exception(e.responseText)}}})},warning:function(t,e){var i,s=this.get_dialogue_element(z),n=this.get_dialogue_element(Y),a=15,o="assignfeedback_editpdf_warningmessages alert alert-warning";e&&(a=4,o="assignfeedback_editpdf_warningmessages alert alert-info"),(i=u.Node.create('<div class="'+o+'" role="alert"></div>')).append(s.one("*").cloneNode()),i.append(t),n.prepend(i),i.transition({duration:1,delay:a,opacity:0},function(){i.remove()})},prepare_pages_for_display:function(t){var e,i,s,n;if(!t.pagecount)return this.dialogue&&this.dialogue.hide(),void new M.core.alert({message:M.util.get_string("cannotopenpdf","assignfeedback_editpdf")}).show();for(this.pagecount=t.pagecount,this.pages=t.pages,e=0;e<this.pages.length;e++){for(i=0;i<this.pages[e].comments.length;i++)s=this.pages[e].comments[i],this.pages[e].comments[i]=new M.assignfeedback_editpdf.comment(this,s.gradeid,s.pageno,s.x,s.y,s.width,s.colour,s.rawtext);for(i=0;i<this.pages[e].annotations.length;i++)n=this.pages[e].annotations[i],this.pages[e].annotations[i]=this.create_annotation(n.type,n)}!this.get("readonly")&&t.partial&&this.warning(M.util.get_string("partialwarning","assignfeedback_editpdf"),!1),this.quicklist&&this.quicklist.load(),this.setup_navigation(),this.setup_toolbar(),this.change_page()},update_page_load_progress:function(){var s,n=0;this.get_dialogue_element(C+" .bar")&&(s={method:"get",context:this,sync:!1,data:{sesskey:M.cfg.sesskey,action:"conversionstatus",userid:this.get("userid"),attemptnumber:this.get("attemptnumber"),assignmentid:this.get("assignmentid")},on:{success:function(t,e){var i;n=0;(i=this.get_dialogue_element(C+" .bar"))&&(e=e.response/this.pagecount*100,i.setStyle("width",e+"%"),i.ancestor(C).setAttribute("aria-valuenow",e),e<100&&(M.util.js_pending("checkconversionstatus"),u.later(1e3,this,function(){M.util.js_complete("checkconversionstatus"),u.io(y,s)})))},failure:function(t,e){return n+=1,0===this.pagecount&&n<5&&(M.util.js_pending("checkconversionstatus"),u.later(1e3,this,function(){M.util.js_complete("checkconversionstatus"),u.io(y,s)})),new M.core.exception(e.responseText)}}},M.util.js_pending("checkconversionstatus"),u.later(1e3,this,function(){n=0,M.util.js_complete("checkconversionstatus"),u.io(y,s)}))},handle_response_data:function(t){var e;try{if(!(e=u.JSON.parse(t.responseText)).error)return e;this.dialogue&&this.dialogue.hide(),new M.core.alert({message:M.util.get_string("cannotopenpdf","assignfeedback_editpdf"),visible:!0})}catch(i){this.dialogue&&this.dialogue.hide(),new M.core.alert({title:M.util.get_string("cannotopenpdf","assignfeedback_editpdf"),visible:!0})}},get_stamp_image_url:function(e){var t=this.get("stampfiles"),i="";return u.Array.each(t,function(t){0<t.indexOf(e)&&(i=t)},this),i},setup_toolbar:function(){var i,t,e=this.get_dialogue_element(A);e.on("click",this.open_search_comments,this),e.on("key",this.open_search_comments,"down:13",this),(e=this.get_dialogue_element(S)).on("click",this.expandCollapseComments,this),e.on("key",this.expandCollapseComments,"down:13",this),this.get("readonly")||((e=this.get_dialogue_element(P)).on("click",this.rotatePDF,this,!0),e.on("key",this.rotatePDF,"down:13",this,!0),(e=this.get_dialogue_element(j)).on("click",this.rotatePDF,this,!1),e.on("key",this.rotatePDF,"down:13",this,!1),this.disable_touch_scroll(),u.each(B,function(t,e){(i=this.get_dialogue_element(t)).on("click",this.handle_tool_button,this,e),i.on("key",this.handle_tool_button,"down:13",this,e),i.setAttribute("aria-pressed","false")},this),e=this.get_dialogue_element(q),new M.assignfeedback_editpdf.colourpicker({buttonNode:e,colours:O,iconprefix:"background_colour_",callback:function(t){var e=(e=t.target.getAttribute("data-colour"))||t.target.ancestor().getAttribute("data-colour");this.currentedit.commentcolour=e,this.handle_tool_button(t,"comment")},context:this}),e=this.get_dialogue_element(E),new M.assignfeedback_editpdf.colourpicker({buttonNode:e,iconprefix:"colour_",colours:d,callback:function(t){var e=(e=t.target.getAttribute("data-colour"))||t.target.ancestor().getAttribute("data-colour");this.currentedit.annotationcolour=e,this.lastannotationtool?this.handle_tool_button(t,this.lastannotationtool):this.handle_tool_button(t,"pen")},context:this}),(e=this.get("stampfiles")).length<=0?this.get_dialogue_element(B.stamp).ancestor().hide():(t=e[0].substr(e[0].lastIndexOf("/")+1),this.currentedit.stamp=t,t=this.get_dialogue_element(X),new M.assignfeedback_editpdf.stamppicker({buttonNode:t,stamps:e,callback:function(t){var e=t.target.getAttribute("data-stamp"),e=(e=e||t.target.ancestor().getAttribute("data-stamp")).substr(e.lastIndexOf("/"));this.currentedit.stamp=e,this.handle_tool_button(t,"stamp")},context:this}),this.refresh_button_state()))},handle_tool_button:function(t,e){t.preventDefault(),(t=this.get_dialogue_element(B[this.currentedit.tool])).removeClass("assignfeedback_editpdf_selectedbutton"),t.setAttribute("aria-pressed","false"),"comment"!==(this.currentedit.tool=e
)&&"select"!==e&&"drag"!==e&&"stamp"!==e&&(this.lastannotationtool=e),this.refresh_button_state()},stringify_current_page:function(){for(var t=[],e=[],i=0,i=0;i<this.pages[this.currentpage].comments.length;i++)t[i]=this.pages[this.currentpage].comments[i].clean();for(i=0;i<this.pages[this.currentpage].annotations.length;i++)e[i]=this.pages[this.currentpage].annotations[i].clean();return u.JSON.stringify({comments:t,annotations:e})},get_current_drawable:function(){var t,e=!1;return!(!this.currentedit.start||!this.currentedit.end)&&("comment"===this.currentedit.tool?e=new M.assignfeedback_editpdf.comment(this).draw_current_edit(this.currentedit):(t=this.create_annotation(this.currentedit.tool,{}))&&(e=t.draw_current_edit(this.currentedit)),e)},get_dialogue_element:function(t){return(this.panel||this.dialogue.get("boundingBox")).one(t)},redraw_current_edit:function(){this.currentdrawable&&this.currentdrawable.erase(),this.currentdrawable=this.get_current_drawable()},edit_start:function(t){var e,i,s=this.get_dialogue_element(g),n=s.getXY(),a=s.get("docScrollY"),s=s.get("docScrollX"),s={x:t.clientX-n[0]+s,y:t.clientY-n[1]+a},o=!1;3!==t.button&&(this.currentedit.starttime||this.editingcomment||(this.currentedit.starttime=(new Date).getTime(),this.currentedit.start=s,this.currentedit.end={x:s.x,y:s.y},"select"===this.currentedit.tool&&(e=this.currentedit.end.x,i=this.currentedit.end.y,n=this.pages[this.currentpage].annotations,u.each(n,function(t){(e-t.x)*(e-t.endx)<=0&&(i-t.y)*(i-t.endy)<=0&&(o=t)}),o?(this.lastannotation=this.currentannotation,this.currentannotation=o,this.lastannotation&&this.lastannotation!==o&&this.lastannotation.drawable&&(this.lastannotation.drawable.erase(),this.drawables.push(this.lastannotation.draw())),this.currentannotation.drawable&&this.currentannotation.drawable.erase(),this.drawables.push(this.currentannotation.draw())):(this.lastannotation=this.currentannotation,this.currentannotation=null,this.lastannotation&&this.lastannotation.drawable&&(this.lastannotation.drawable.erase(),this.drawables.push(this.lastannotation.draw())))),this.currentannotation&&(this.currentedit.annotationstart={x:this.currentannotation.x,y:this.currentannotation.y})))},edit_move:function(t){var e=this.get_canvas_bounds(),i=this.get_dialogue_element(g),s=this.get_dialogue_element(r),i=new M.assignfeedback_editpdf.point(t.clientX+i.get("docScrollX"),t.clientY+i.get("docScrollY")),i=this.get_canvas_coordinates(i);"textarea"!==document.activeElement.type&&(t.preventDefault(),i.x<0||i.x>e.width||i.y<0||i.y>e.height||("pen"===this.currentedit.tool&&this.currentedit.path.push(i),"select"===this.currentedit.tool?this.currentannotation&&this.currentedit&&this.currentannotation.move(this.currentedit.annotationstart.x+i.x-this.currentedit.start.x,this.currentedit.annotationstart.y+i.y-this.currentedit.start.y):"drag"===this.currentedit.tool?(t=i.x-this.currentedit.start.x,e=i.y-this.currentedit.start.y,s.getDOMNode().scrollLeft-=t,s.getDOMNode().scrollTop-=e):this.currentedit.start&&(this.currentedit.end=i,this.redraw_current_edit())))},edit_end:function(){var t;(new Date).getTime()-this.currentedit.start<300||!1===this.currentedit.start||("comment"===this.currentedit.tool?(this.currentdrawable&&this.currentdrawable.erase(),this.currentdrawable=!1,(t=new M.assignfeedback_editpdf.comment(this)).init_from_edit(this.currentedit)&&(this.pages[this.currentpage].comments.push(t),this.drawables.push(t.draw(!0)),this.editingcomment=!0)):(t=this.create_annotation(this.currentedit.tool,{}))&&(this.currentdrawable&&this.currentdrawable.erase(),this.currentdrawable=!1,t.init_from_edit(this.currentedit)&&(this.pages[this.currentpage].annotations.push(t),this.drawables.push(t.draw()))),this.save_current_page(),this.currentedit.starttime=0,this.currentedit.start=!1,this.currentedit.end=!1,this.currentedit.path=[])},resize:function(){var t,e;if(this.dialogue){if(!this.dialogue.get("visible"))return;this.dialogue.centerDialogue()}return(e=u.one("body").get("winHeight")-120)<100&&(e=100),t=this.get_dialogue_element(r),this.dialogue&&t.setStyle("maxHeight",e+"px"),this.redraw(),!0},create_annotation:function(t,e){return e.type=t,e.editor=this,"line"===t?new M.assignfeedback_editpdf.annotationline(e):"rectangle"===t?new M.assignfeedback_editpdf.annotationrectangle(e):"oval"===t?new M.assignfeedback_editpdf.annotationoval(e):"pen"===t?new M.assignfeedback_editpdf.annotationpen(e):"highlight"===t?new M.assignfeedback_editpdf.annotationhighlight(e):"stamp"===t&&new M.assignfeedback_editpdf.annotationstamp(e)},save_current_page:function(){this.clear_warnings(!1);var t=o,e={method:"post",context:this,sync:!1,data:{sesskey:M.cfg.sesskey,action:"savepage",index:this.currentpage,userid:this.get("userid"),attemptnumber:this.get("attemptnumber"),assignmentid:this.get("assignmentid"),page:this.stringify_current_page()},on:{success:function(t,e){var i;try{if((i=u.JSON.parse(e.responseText)).error)return new M.core.ajaxException(i);u.one(R).set("value","true"),this.warning(M.util.get_string("draftchangessaved","assignfeedback_editpdf"),!0)}catch(s){return new M.core.exception(s)}},failure:function(t,e){return new M.core.exception(e.responseText)}}};u.io(t,e)},open_search_comments:function(t){this.searchcommentswindow||(this.searchcommentswindow=new M.assignfeedback_editpdf.commentsearch({editor:this})),this.searchcommentswindow.show(),t.preventDefault()},expandCollapseComments:function(){var t=u.all(".commentdrawable");this.collapsecomments?(this.collapsecomments=!1,t.removeClass("commentcollapsed")):(this.collapsecomments=!0,t.addClass("commentcollapsed"))},redraw:function(){var t,e=this.pages[this.currentpage];if(e!==undefined){for(;0<this.drawables.length;)this.drawables.pop().erase();for(t=0;t<e.annotations.length;t++)this.drawables.push(e.annotations[t].draw());for(t=0;t<e.comments.length;t++)this.drawables.push(e.comments[t].draw(!1))}},clear_warnings:function(t){var e=this.get_dialogue_element(Y);t?e.empty():e.all(
".alert-info").remove(!0)},change_page:function(){var t=this.get_dialogue_element(g),e=this.get_dialogue_element(v),i=this.get_dialogue_element(x);0<this.currentpage?e.removeAttribute("disabled"):e.setAttribute("disabled","true"),this.currentpage<this.pagecount-1?i.removeAttribute("disabled"):i.setAttribute("disabled","true"),e=this.pages[this.currentpage],this.loadingicon&&this.loadingicon.hide(),t.setStyle("backgroundImage",'url("'+e.url+'")'),t.setStyle("width",e.width+"px"),t.setStyle("height",e.height+"px"),t.scrollIntoView(),this.get_dialogue_element(T).set("selectedIndex",this.currentpage),this.resize()},setup_navigation:function(){var t,e,i,s,n=this.get_dialogue_element(T),a=n.all("option");if(a.size()<=1)for(t=0;t<this.pages.length;t++)(i=u.Node.create("<option/>")).setAttribute("value",t),e={page:t+1,total:this.pages.length},i.setHTML(M.util.get_string("pagexofy","assignfeedback_editpdf",e)),n.append(i);n.removeAttribute("disabled"),n.on("change",function(){this.currentpage=n.get("value"),this.clear_warnings(!1),this.change_page()},this),a=this.get_dialogue_element(v),s=this.get_dialogue_element(x),a.on("click",this.previous_page,this),a.on("key",this.previous_page,"down:13",this),s.on("click",this.next_page,this),s.on("key",this.next_page,"down:13",this)},previous_page:function(t){t.preventDefault(),this.currentpage--,this.currentpage<0&&(this.currentpage=0),this.clear_warnings(!1),this.change_page()},next_page:function(t){t.preventDefault(),this.currentpage++,this.currentpage>=this.pages.length&&(this.currentpage=this.pages.length-1),this.clear_warnings(!1),this.change_page()},move_canvas:function(){for(var t=this.get_dialogue_element(r),e=parseInt(t.get("scrollLeft"),10),i=parseInt(t.get("scrollTop"),10),s=0;s<this.drawables.length;s++)this.drawables[s].scroll_update(e,i)},rotatePDF:function(h,t){var l,e,i,s,n;if(h.preventDefault(),!this.get("destroyed")){for((l=this).oldannotationcoordinates=[],i=this.pages[this.currentpage].annotations,e=0;e<i.length;e++)s=i[e],this.oldannotationcoordinates.push([s.x,s.y]);n=o,t={method:"post",context:this,sync:!1,data:{sesskey:M.cfg.sesskey,action:"rotatepage",index:this.currentpage,userid:this.get("userid"),attemptnumber:this.get("attemptnumber"),assignmentid:this.get("assignmentid"),rotateleft:t},on:{success:function(c,t){var e,i,s,n,a,o,r,d;try{for(e=u.JSON.parse(t.responseText),(i=l.pages[l.currentpage]).url=e.page.url,i.width=e.page.width,i.height=e.page.height,l.loadingicon.hide(),(s=l.get_dialogue_element(g)).setStyle("backgroundImage",'url("'+i.url+'")'),s.setStyle("width",i.width+"px"),s.setStyle("height",i.height+"px"),a=i.annotations,n=0;n<a.length;n++)l.oldannotationcoordinates&&l.oldannotationcoordinates[n]&&(o=l.oldannotationcoordinates[n][0],r=l.oldannotationcoordinates[n][1],a[n].move(o,r));for(d=i.comments,n=0;n<d.length;n++)d[n].updatePosition();return l.save_current_page()}catch(h){return new M.core.exception(h)}},failure:function(t,e){return new M.core.exception(e.responseText)}}},u.io(n,t)}},event_listener_options_supported:function(){var t,e=!1,i="testpassiveeventoptions";try{t=Object.defineProperty({},"passive",{get:function(){e=!0}}),document.addEventListener(i,t,t),document.removeEventListener(i,t,t)}catch(s){e=!1}return e},disable_touch_scroll:function(){this.event_listener_options_supported()&&document.addEventListener("touchmove",this.stop_touch_scroll.bind(this),{passive:!1})},stop_touch_scroll:function(t){this.get_dialogue_element(r).contains(t.target)&&(t.stopPropagation(),t.preventDefault())}},u.extend(w,u.Base,w.prototype,{NAME:"moodle-assignfeedback_editpdf-editor",ATTRS:{userid:{validator:u.Lang.isInteger,value:0},assignmentid:{validator:u.Lang.isInteger,value:0},attemptnumber:{validator:u.Lang.isInteger,value:0},header:{validator:u.Lang.isString,value:""},body:{validator:u.Lang.isString,value:""},footer:{validator:u.Lang.isString,value:""},linkid:{validator:u.Lang.isString,value:""},deletelinkid:{validator:u.Lang.isString,value:""},readonly:{validator:u.Lang.isBoolean,value:!0},stampfiles:{validator:u.Lang.isArray,value:""}}}),M.assignfeedback_editpdf=M.assignfeedback_editpdf||{},M.assignfeedback_editpdf.editor=M.assignfeedback_editpdf.editor||{},M.assignfeedback_editpdf.editor.init=M.assignfeedback_editpdf.editor.init||function(t){return M.assignfeedback_editpdf.instance=new w(t),M.assignfeedback_editpdf.instance}},"@VERSION@",{requires:["base","event","node","io","graphics","json","event-move","event-resize","transition","querystring-stringify-simple","moodle-core-notification-dialog","moodle-core-notification-alert","moodle-core-notification-warning","moodle-core-notification-exception","moodle-core-notification-ajaxexception"]});