<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * This file contains the moodle hooks for the submission comments plugin
 *
 * @package   assignsubmission_comments
 * @copyright 2012 NetSpot {@link http://www.netspot.com.au}
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
defined('MOODLE_INTERNAL') || die();

/**
 *
 * Callback method for data validation---- required method for AJAXmoodle based comment API
 *
 * @param stdClass $options
 * @return bool
 */
function assignsubmission_comments_comment_validate(stdClass $options) {
    global $USER, $CFG, $DB;

    if ($options->commentarea != 'submission_comments' &&
            $options->commentarea != 'submission_comments_upgrade') {
        throw new comment_exception('invalidcommentarea');
    }
    if (!$submission = $DB->get_record('assign_submission', array('id'=>$options->itemid))) {
        throw new comment_exception('invalidcommentitemid');
    }
    $context = $options->context;

    require_once($CFG->dirroot . '/mod/assign/locallib.php');
    static $assignment = null;
    if (is_null($assignment) || $assignment->get_context() != $context) {
        $assignment = new assign($context, null, null);
    }

    if ($assignment->get_instance()->id != $submission->assignment) {
        throw new comment_exception('invalidcontext');
    }

    return true;
}

/**
 * Permission control method for submission plugin ---- required method for AJAXmoodle based comment API
 *
 * @param stdClass $options
 * @return array
 */
function assignsubmission_comments_comment_permissions(stdClass $options) {
    global $USER, $CFG, $DB;

    if ($options->commentarea != 'submission_comments' &&
            $options->commentarea != 'submission_comments_upgrade') {
        throw new comment_exception('invalidcommentarea');
    }
    if (!$submission = $DB->get_record('assign_submission', array('id'=>$options->itemid))) {
        throw new comment_exception('invalidcommentitemid');
    }
    $context = $options->context;

    require_once($CFG->dirroot . '/mod/assign/locallib.php');
    static $assignment = null;
    if (is_null($assignment) || $assignment->get_context() != $context) {
        $assignment = new assign($context, null, null);
    }

    if ($assignment->get_instance()->id != $submission->assignment) {
        throw new comment_exception('invalidcontext');
    }

    if ($assignment->get_instance()->teamsubmission &&
        !$assignment->can_view_group_submission($submission->groupid)) {
        return array('post' => false, 'view' => false);
    }

    if (!$assignment->get_instance()->teamsubmission &&
        !$assignment->can_view_submission($submission->userid)) {
        return array('post' => false, 'view' => false);
    }

    return array('post' => true, 'view' => true);
}

/**
 * Callback called by comment::get_comments() and comment::add(). Gives an opportunity to enforce blind-marking.
 *
 * @param array $comments
 * @param stdClass $options
 * @return array
 * @throws comment_exception
 */
function assignsubmission_comments_comment_display($comments, $options) {
    global $CFG, $DB, $USER;

    if ($options->commentarea != 'submission_comments' &&
        $options->commentarea != 'submission_comments_upgrade') {
        throw new comment_exception('invalidcommentarea');
    }
    if (!$submission = $DB->get_record('assign_submission', array('id'=>$options->itemid))) {
        throw new comment_exception('invalidcommentitemid');
    }
    $context = $options->context;
    $cm = $options->cm;
    $course = $options->courseid;

    require_once($CFG->dirroot . '/mod/assign/locallib.php');
    $assignment = new assign($context, $cm, $course);

    if ($assignment->get_instance()->id != $submission->assignment) {
        throw new comment_exception('invalidcontext');
    }

    if ($assignment->is_blind_marking() && !empty($comments)) {
        // Blind marking is being used, may need to map unique anonymous ids to the comments.
        $usermappings = array();
        $guestuser = guest_user();

        // Check group users first.
        $userinteam = false;
        if ($assignment->get_instance()->teamsubmission && has_capability('mod/assign:submit', $context)) {
            $assignment->set_course(get_course($course));
            $userinteam = $assignment->can_edit_group_submission($submission->groupid);
        }

        foreach ($comments as $comment) {

            if (has_capability('mod/assign:viewblinddetails', $context) && $USER->id != $comment->userid) {
                $anonid = $assignment->get_uniqueid_for_user($comment->userid);
                // Show participant information and the user's full name to users with the view blind details capability.
                $a = new stdClass();
                $a->participantnumber = $anonid;
                $a->participantfullname = $comment->fullname;
                $comment->fullname = get_string('blindmarkingviewfullname', 'assignsubmission_comments', $a);
            } else if ($USER->id == $comment->userid || $submission->userid == $USER->id || $userinteam) { // phpcs:ignore
                // Do not anonymize the user details for this comment.
            } else {
                // Anonymize the comments.
                if (empty($usermappings[$comment->userid])) {
                    $anonid = $assignment->get_uniqueid_for_user($comment->userid);
                    // The blind-marking information for this commenter has not been generated; do so now.
                    $commenter = new stdClass();
                    $commenter->firstname = get_string('blindmarkingname', 'assignsubmission_comments', $anonid);
                    $commenter->lastname = '';
                    $commenter->firstnamephonetic = '';
                    $commenter->lastnamephonetic = '';
                    $commenter->middlename = '';
                    $commenter->alternatename = '';
                    $commenter->picture = 0;
                    $commenter->id = $guestuser->id;
                    $commenter->email = $guestuser->email;
                    $commenter->imagealt = $guestuser->imagealt;

                    // Temporarily store blind-marking information for use in later comments if necessary.
                    $usermappings[$comment->userid] = new stdClass();
                    $usermappings[$comment->userid]->fullname = fullname($commenter);
                    $usermappings[$comment->userid]->avatar = $assignment->get_renderer()->user_picture($commenter,
                            array('size' => 18, 'link' => false));
                }

                // Set blind-marking information for this comment.
                $comment->fullname = $usermappings[$comment->userid]->fullname;
                $comment->avatar = $usermappings[$comment->userid]->avatar;
                $comment->profileurl = null;
            }
        }
    }

    return $comments;
}

/**
 * Callback to force the userid for all comments to be the userid of the submission and NOT the global $USER->id. This
 * is required by the upgrade code. Note the comment area is used to identify upgrades.
 *
 * @param stdClass $comment
 * @param stdClass $param
 */
function assignsubmission_comments_comment_add(stdClass $comment, stdClass $param) {

    global $DB;
    if ($comment->commentarea == 'submission_comments_upgrade') {
        $submissionid = $comment->itemid;
        $submission = $DB->get_record('assign_submission', array('id' => $submissionid));

        $comment->userid = $submission->userid;
        $comment->commentarea = 'submission_comments';
    }
}

