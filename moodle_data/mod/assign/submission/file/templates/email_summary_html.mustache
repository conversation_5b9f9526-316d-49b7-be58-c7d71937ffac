{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template assignsubmission_file/email_summary_html

    This template is used render the HTML version of the summary of files submissions to go into emails.

    Classes required for JS:
    * none

    Context variables required for this template:
    * files - array of sumbitted files, each with properties filepath and filesize.

    Example context (json):
    {
        "files": [
            {
                "filepath": "File 1.docx",
                "filesize": "12.3KB"
            },
            {
                "filepath": "subdir/extra data.txt",
                "filesize": "456B"
            }
        ]
    }
}}
<h3>{{#str}}file, assignsubmission_file{{/str}}</h3>

<ul>
{{#files}}
    <li>{{#str}}filewithsize, assignsubmission_file, {"filename": {{#quote}}{{filepath}}{{/quote}}, "size": {{#quote}}{{filesize}}{{/quote}}, "coursename": {{#quote}}{{coursename}}{{/quote}} } {{/str}}</li>
{{/files}}
</ul>
