<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Strings for component 'assignsubmission_onlinetext', language 'en'
 *
 * @package   assignsubmission_onlinetext
 * @copyright 2012 NetSpot {@link http://www.netspot.com.au}
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['allowonlinetextsubmissions'] = 'Enabled';
$string['default'] = 'Enabled by default';
$string['default_help'] = 'If set, this submission method will be enabled by default for all new assignments.';
$string['enabled'] = 'Online text';
$string['enabled_help'] = 'If enabled, students are able to type rich text directly into an editor field for their submission.';
$string['eventassessableuploaded'] = 'An online text has been uploaded.';
$string['nosubmission'] = 'Nothing has been submitted for this assignment';
$string['onlinetext'] = 'Online text';
$string['onlinetextfilename'] = 'onlinetext.html';
$string['onlinetextsubmission'] = 'Allow online text submission';
$string['numwords'] = '({$a} words)';
$string['pluginname'] = 'Online text submissions';
$string['privacy:metadata:assignmentid'] = 'Assignment ID';
$string['privacy:metadata:filepurpose'] = 'Files that are embedded in the text submission.';
$string['privacy:metadata:submissionpurpose'] = 'The submission ID that links to submissions for the user.';
$string['privacy:metadata:tablepurpose'] = 'Stores the text submission for each attempt.';
$string['privacy:metadata:textpurpose'] = 'The actual text submitted for this attempt of the assignment.';
$string['privacy:path'] = 'Submission Text';
$string['wordlimit'] = 'Word limit';
$string['wordlimit_help'] = 'If online text submissions are enabled, this is the maximum number of words that each student will be allowed to submit.';
$string['wordlimitexceeded'] = 'The word limit for this assignment is {$a->limit} words and you are attempting to submit {$a->count} words. Please review your submission and try again.';
