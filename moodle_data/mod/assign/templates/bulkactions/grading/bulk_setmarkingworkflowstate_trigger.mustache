{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template mod_assign/bulkactions/grading/bulk_setmarkingworkflowstate_trigger

    Renders the bulk set marking workflow state trigger element in assignment grading page.

    Context variables required for this template:
    * showindropdown - Whether the action is displayed under a 'More' dropdown or as a separate button.
    * isfirst - Whether the action is the first one in the list.

    Example context (json):
    {
        "showindropdown": false,
        "isfirst": true
    }
}}
{{< core/bulkactions/bulk_action_trigger }}
    {{$action}}setmarkingworkflowstate{{/action}}
    {{$title}}{{#str}} batchoperationsetmarkingworkflowstate, mod_assign {{/str}}{{/title}}
    {{$icon}}<i class="icon fa fa-diagram-project fa-fw"></i>{{/icon}}
{{/ core/bulkactions/bulk_action_trigger }}
