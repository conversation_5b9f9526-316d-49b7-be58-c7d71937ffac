{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_my/dropdown

    Simple dropdown for the my/courses page.

    Example context (json):
    {
        "newcourseurl": "https://moodle.test/course/edit.php?category=1",
        "manageurl": "https://moodle.test/course/management.php?categoryid=1",
        "courserequesturl": "https://moodle.test/course/request.php?categoryid=1"
    }
}}
<div class="btn-group{{#manageurl}} course-manage{{/manageurl}}{{#courserequesturl}} course-request{{/courserequesturl}}">
    <div class="my-action-buttons my-action-buttons-right">
        {{#manageurl}}
            <form action="{{manageurl}}" method="post" id="managecoursesform">
                <button type="submit" class="btn btn-outline-primary m-1 w-100">{{#str}} managecourses {{/str}}</button>
            </form>
        {{/manageurl}}
        {{#newcourseurl}}
            <form action="{{newcourseurl}}" method="post" id="newcourseform">
                <button type="submit" class="btn btn-primary m-1 w-100">{{#str}} createcourse, block_myoverview {{/str}}</button>
            </form>
        {{/newcourseurl}}
        {{#courserequesturl}}
            <form action="{{courserequesturl}}" method="post" id="courserequestform">
                <button type="submit" class="btn btn-primary m-1 w-100">{{#str}} requestcourse {{/str}}</button>
            </form>
        {{/courserequesturl}}
    </div>
</div>
