@core @core_my
Feature: Add blocks to dashboard page
  In order to add more functionality to dashboard page
  As a user
  I need to add blocks to dashboard page

  Background:
    Given the following "users" exist:
      | username | firstname | lastname | email |
      | student1 | Student | 1 | <EMAIL> |
      | student2 | Student | 2 | <EMAIL> |
    And the following "courses" exist:
      | fullname | shortname | format |
      | Course 1 | C1 | topics |
    And the following "course enrolments" exist:
      | user | course | role |
      | student1 | C1 | student |
      | student2 | C1 | student |
    And I log in as "student1"

  Scenario: Add blocks to page
    When I turn editing mode on
    And I add the "Latest announcements" block
    And I turn editing mode off
    Then I should see "Latest announcements" in the "Latest announcements" "block"
    And I should see "Timeline" in the "Timeline" "block"
    And I should see "Calendar" in the "Calendar" "block"
    And I should not see "Upcoming events"
