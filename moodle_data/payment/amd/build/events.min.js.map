{"version": 3, "file": "events.min.js", "sources": ["../src/events.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Contain the events the payment modal can fire.\n *\n * @module     core_payment/events\n * @copyright  2020 Shamim <PERSON>zaie <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nexport default {\n    proceed: 'core_payment-modal_gateways:proceed',\n};\n"], "names": ["proceed"], "mappings": "qKAuBe,CACXA,QAAS"}