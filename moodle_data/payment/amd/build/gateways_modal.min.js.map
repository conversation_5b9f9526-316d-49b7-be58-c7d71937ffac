{"version": 3, "file": "gateways_modal.min.js", "sources": ["../src/gateways_modal.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Contain the logic for the gateways modal.\n *\n * @module     core_payment/gateways_modal\n * @copyright  2019 Shamim <PERSON>zaie <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Templates from 'core/templates';\nimport {getString} from 'core/str';\nimport {getAvailableGateways} from './repository';\nimport Selectors from './selectors';\nimport ModalEvents from 'core/modal_events';\nimport PaymentEvents from 'core_payment/events';\nimport {add as addToast, addToastRegion} from 'core/toast';\nimport Notification from 'core/notification';\nimport ModalGateways from './modal_gateways';\n\n/**\n * Register event listeners for the module.\n */\nconst registerEventListeners = () => {\n    document.addEventListener('click', e => {\n        const gatewayTrigger = e.target.closest('[data-action=\"core_payment/triggerPayment\"]');\n        if (gatewayTrigger) {\n            e.preventDefault();\n\n            show(gatewayTrigger, {focusOnClose: e.target});\n        }\n    });\n};\n\n/**\n * Shows the gateway selector modal.\n *\n * @param {HTMLElement} rootNode\n * @param {Object} options - Additional options\n * @param {HTMLElement} options.focusOnClose The element to focus on when the modal is closed.\n */\nconst show = async(rootNode, {\n    focusOnClose = null,\n} = {}) => {\n\n    // Load upfront, so we don't try to inject the internal content into a possibly-not-yet-resolved promise.\n    const body = await Templates.render('core_payment/gateways_modal', {});\n\n    const modal = await ModalGateways.create({\n        title: getString('selectpaymenttype', 'core_payment'),\n        body: body,\n        show: true,\n        removeOnClose: true,\n    });\n\n    const rootElement = modal.getRoot()[0];\n    addToastRegion(rootElement);\n\n    modal.getRoot().on(ModalEvents.hidden, () => {\n        focusOnClose?.focus();\n    });\n\n    modal.getRoot().on(PaymentEvents.proceed, async(e) => {\n        e.preventDefault();\n        const gateway = (rootElement.querySelector(Selectors.values.gateway) || {value: ''}).value;\n\n        if (gateway) {\n            processPayment(\n                gateway,\n                rootNode.dataset.component,\n                rootNode.dataset.paymentarea,\n                rootNode.dataset.itemid,\n                rootNode.dataset.description\n            ).then((message) => {\n                modal.hide();\n                Notification.addNotification({\n                    message,\n                    type: 'success',\n                });\n                location.href = rootNode.dataset.successurl;\n\n                return;\n            }).catch(message => Notification.alert('', message));\n        } else {\n            // We cannot use await in the following line.\n            // The reason is that we are preventing the default action of the save event being triggered,\n            // therefore we cannot define the event handler function asynchronous.\n            addToast(getString('nogatewayselected', 'core_payment'), {\n                type: 'warning',\n            });\n        }\n    });\n\n    // Re-calculate the cost when gateway is changed.\n    rootElement.addEventListener('change', e => {\n        if (e.target.matches(Selectors.elements.gateways)) {\n            updateCostRegion(rootElement, rootNode.dataset.cost);\n        }\n    });\n\n    const gateways = await getAvailableGateways(rootNode.dataset.component, rootNode.dataset.paymentarea, rootNode.dataset.itemid);\n    const context = {\n        gateways\n    };\n\n    const {html, js} = await Templates.renderForPromise('core_payment/gateways', context);\n    Templates.replaceNodeContents(rootElement.querySelector(Selectors.regions.gatewaysContainer), html, js);\n    selectSingleGateway(rootElement);\n    await updateCostRegion(rootElement, rootNode.dataset.cost);\n};\n\n/**\n * Auto-select the gateway if there is only one gateway.\n *\n * @param {HTMLElement} root An HTMLElement that contains the cost region\n */\nconst selectSingleGateway = root => {\n    const gateways = root.querySelectorAll(Selectors.elements.gateways);\n\n    if (gateways.length == 1) {\n        gateways[0].checked = true;\n    }\n};\n\n/**\n * Shows the cost of the item the user is purchasing in the cost region.\n *\n * @param {HTMLElement} root An HTMLElement that contains the cost region\n * @param {string} defaultCost The default cost that is going to be displayed if no gateway is selected\n * @returns {Promise<void>}\n */\nconst updateCostRegion = async(root, defaultCost = '') => {\n    const gatewayElement = root.querySelector(Selectors.values.gateway);\n    const surcharge = parseInt((gatewayElement || {dataset: {surcharge: 0}}).dataset.surcharge);\n    const cost = (gatewayElement || {dataset: {cost: defaultCost}}).dataset.cost;\n    const valueStr = surcharge ? await getString('feeincludesurcharge', 'core_payment', {fee: cost, surcharge: surcharge}) : cost;\n\n    const surchargeStr = await getString('labelvalue', 'core',\n        {\n            label: await getString('cost', 'core'),\n            value: valueStr\n        }\n    );\n\n    const {html, js} = await Templates.renderForPromise('core_payment/fee_breakdown', {surchargestr: surchargeStr});\n    Templates.replaceNodeContents(root.querySelector(Selectors.regions.costContainer), html, js);\n};\n\n/**\n * Process payment using the selected gateway.\n *\n * @param {string} gateway The gateway to be used for payment\n * @param {string} component Name of the component that the itemId belongs to\n * @param {string} paymentArea Name of the area in the component that the itemId belongs to\n * @param {number} itemId An internal identifier that is used by the component\n * @param {string} description Description of the payment\n * @returns {Promise<string>}\n */\nconst processPayment = async(gateway, component, paymentArea, itemId, description) => {\n    const paymentMethod = await import(`paygw_${gateway}/gateways_modal`);\n    return paymentMethod.process(component, paymentArea, itemId, description);\n};\n\n/**\n * Set up the payment actions.\n */\nexport const init = () => {\n    if (!init.initialised) {\n        // Event listeners should only be registered once.\n        init.initialised = true;\n        registerEventListeners();\n    }\n};\n\n/**\n * Whether the init function was called before.\n *\n * @static\n * @type {boolean}\n */\ninit.initialised = false;\n"], "names": ["show", "async", "rootNode", "focusOnClose", "body", "Templates", "render", "modal", "ModalGateways", "create", "title", "removeOnClose", "rootElement", "getRoot", "on", "ModalEvents", "hidden", "focus", "PaymentEvents", "proceed", "e", "preventDefault", "gateway", "querySelector", "Selectors", "values", "value", "processPayment", "dataset", "component", "paymentarea", "itemid", "description", "then", "message", "hide", "addNotification", "type", "location", "href", "<PERSON><PERSON>l", "catch", "Notification", "alert", "addEventListener", "target", "matches", "elements", "gateways", "updateCostRegion", "cost", "context", "html", "js", "renderForPromise", "replaceNodeContents", "regions", "gatewaysContainer", "selectSingleGateway", "root", "querySelectorAll", "length", "checked", "defaultCost", "gatewayElement", "surcharge", "parseInt", "valueStr", "fee", "surchargeStr", "label", "surchargestr", "costContainer", "paymentArea", "itemId", "process", "init", "initialised", "document", "gatewayTrigger", "closest"], "mappings": "y5BAsDMA,KAAOC,eAAMC,cAAUC,aACzBA,aAAe,6DACf,SAGMC,WAAaC,mBAAUC,OAAO,8BAA+B,IAE7DC,YAAcC,wBAAcC,OAAO,CACrCC,OAAO,kBAAU,oBAAqB,gBACtCN,KAAMA,KACNJ,MAAM,EACNW,eAAe,IAGbC,YAAcL,MAAMM,UAAU,6BACrBD,aAEfL,MAAMM,UAAUC,GAAGC,sBAAYC,QAAQ,KACnCb,MAAAA,cAAAA,aAAcc,WAGlBV,MAAMM,UAAUC,GAAGI,gBAAcC,SAASlB,MAAAA,IACtCmB,EAAEC,uBACIC,SAAWV,YAAYW,cAAcC,mBAAUC,OAAOH,UAAY,CAACI,MAAO,KAAKA,MAEjFJ,QACAK,eACIL,QACApB,SAAS0B,QAAQC,UACjB3B,SAAS0B,QAAQE,YACjB5B,SAAS0B,QAAQG,OACjB7B,SAAS0B,QAAQI,aACnBC,MAAMC,UACJ3B,MAAM4B,6BACOC,gBAAgB,CACzBF,QAAAA,QACAG,KAAM,YAEVC,SAASC,KAAOrC,SAAS0B,QAAQY,cAGlCC,OAAMP,SAAWQ,sBAAaC,MAAM,GAAIT,2BAKlC,kBAAU,oBAAqB,gBAAiB,CACrDG,KAAM,eAMlBzB,YAAYgC,iBAAiB,UAAUxB,IAC/BA,EAAEyB,OAAOC,QAAQtB,mBAAUuB,SAASC,WACpCC,iBAAiBrC,YAAaV,SAAS0B,QAAQsB,eAIjDF,eAAiB,oCAAqB9C,SAAS0B,QAAQC,UAAW3B,SAAS0B,QAAQE,YAAa5B,SAAS0B,QAAQG,QACjHoB,QAAU,CACZH,SAAAA,WAGEI,KAACA,KAADC,GAAOA,UAAYhD,mBAAUiD,iBAAiB,wBAAyBH,4BACnEI,oBAAoB3C,YAAYW,cAAcC,mBAAUgC,QAAQC,mBAAoBL,KAAMC,IACpGK,oBAAoB9C,mBACdqC,iBAAiBrC,YAAaV,SAAS0B,QAAQsB,OAQnDQ,oBAAsBC,aAClBX,SAAWW,KAAKC,iBAAiBpC,mBAAUuB,SAASC,UAEnC,GAAnBA,SAASa,SACTb,SAAS,GAAGc,SAAU,IAWxBb,iBAAmBhD,eAAM0D,UAAMI,mEAAc,SACzCC,eAAiBL,KAAKpC,cAAcC,mBAAUC,OAAOH,SACrD2C,UAAYC,UAAUF,gBAAkB,CAACpC,QAAS,CAACqC,UAAW,KAAKrC,QAAQqC,WAC3Ef,MAAQc,gBAAkB,CAACpC,QAAS,CAACsB,KAAMa,eAAenC,QAAQsB,KAClEiB,SAAWF,gBAAkB,kBAAU,sBAAuB,eAAgB,CAACG,IAAKlB,KAAMe,UAAWA,YAAcf,KAEnHmB,mBAAqB,kBAAU,aAAc,OAC/C,CACIC,YAAa,kBAAU,OAAQ,QAC/B5C,MAAOyC,YAITf,KAACA,KAADC,GAAOA,UAAYhD,mBAAUiD,iBAAiB,6BAA8B,CAACiB,aAAcF,kCACvFd,oBAAoBI,KAAKpC,cAAcC,mBAAUgC,QAAQgB,eAAgBpB,KAAMC,KAavF1B,eAAiB1B,MAAMqB,QAASO,UAAW4C,YAAaC,OAAQ1C,qPACtBV,sUAAAA,sGAAAA,+BACvBqD,QAAQ9C,UAAW4C,YAAaC,OAAQ1C,aAMpD4C,KAAO,KACXA,KAAKC,cAEND,KAAKC,aAAc,EAjJvBC,SAASlC,iBAAiB,SAASxB,UACzB2D,eAAiB3D,EAAEyB,OAAOmC,QAAQ,+CACpCD,iBACA3D,EAAEC,iBAEFrB,KAAK+E,eAAgB,CAAC5E,aAAciB,EAAEyB,kCAuJlD+B,KAAKC,aAAc"}