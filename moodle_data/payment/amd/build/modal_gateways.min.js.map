{"version": 3, "file": "modal_gateways.min.js", "sources": ["../src/modal_gateways.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// Mood<PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Contain the logic for the gateways modal: A modal with proceed and cancel buttons.\n *\n * @module     core_payment/modal_gateways\n * @copyright  2020 Shamim <PERSON>zaie <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport $ from 'jquery';\nimport CustomEvents from 'core/custom_interaction_events';\nimport Modal from 'core/modal';\nimport ModalEvents from 'core/modal_events';\nimport PaymentEvents from 'core_payment/events';\n\nconst SELECTORS = {\n    PROCEED_BUTTON: '[data-action=\"proceed\"]',\n    CANCEL_BUTTON: '[data-action=\"cancel\"]',\n};\n\nexport default class ModalGateways extends Modal {\n    static TYPE = 'core_payment-modal_gateways';\n    static TEMPLATE = 'core_payment/modal_gateways';\n\n    /**\n     * Constructor for the Modal.\n     *\n     * @param {object} root The root jQuery element for the modal\n     */\n    constructor(root) {\n        super(root);\n    }\n\n    /**\n     * Set up all of the event handling for the modal.\n     *\n     * @method registerEventListeners\n     */\n    registerEventListeners() {\n        // Apply parent event listeners.\n        super.registerEventListeners();\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.PROCEED_BUTTON, (e, data) => {\n            var proceedEvent = $.Event(PaymentEvents.proceed);\n            this.getRoot().trigger(proceedEvent, this);\n\n            if (!proceedEvent.isDefaultPrevented()) {\n                this.hide();\n                data.originalEvent.preventDefault();\n            }\n        });\n\n        this.getModal().on(CustomEvents.events.activate, SELECTORS.CANCEL_BUTTON, (e, data) => {\n            var cancelEvent = $.Event(ModalEvents.cancel);\n            this.getRoot().trigger(cancelEvent, this);\n\n            if (!cancelEvent.isDefaultPrevented()) {\n                this.hide();\n                data.originalEvent.preventDefault();\n            }\n        });\n    }\n}\n\nModalGateways.registerModalType();\n"], "names": ["SELECTORS", "ModalGateways", "Modal", "constructor", "root", "registerEventListeners", "getModal", "on", "CustomEvents", "events", "activate", "e", "data", "proceedEvent", "$", "Event", "PaymentEvents", "proceed", "getRoot", "trigger", "this", "isDefaultPrevented", "hide", "originalEvent", "preventDefault", "cancelEvent", "ModalEvents", "cancel", "registerModalType"], "mappings": "0yBA6BMA,yBACc,0BADdA,wBAEa,+BAGEC,sBAAsBC,eASvCC,YAAYC,YACFA,MAQVC,+BAEUA,8BAEDC,WAAWC,GAAGC,mCAAaC,OAAOC,SAAUV,0BAA0B,CAACW,EAAGC,YACvEC,aAAeC,gBAAEC,MAAMC,gBAAcC,cACpCC,UAAUC,QAAQN,aAAcO,MAEhCP,aAAaQ,4BACTC,OACLV,KAAKW,cAAcC,0BAItBlB,WAAWC,GAAGC,mCAAaC,OAAOC,SAAUV,yBAAyB,CAACW,EAAGC,YACtEa,YAAcX,gBAAEC,MAAMW,sBAAYC,aACjCT,UAAUC,QAAQM,YAAaL,MAE/BK,YAAYJ,4BACRC,OACLV,KAAKW,cAAcC,4EAtCdvB,qBACH,+CADGA,yBAEC,+BA0CtBA,cAAc2B"}