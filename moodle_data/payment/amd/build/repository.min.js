define("core_payment/repository",["exports","core/ajax"],(function(_exports,_ajax){var obj;
/**
   * Repository for payment subsystem.
   *
   * @module     core_payment/repository
   * @copyright  2020 <PERSON><PERSON><PERSON> <<EMAIL>>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.getAvailableGateways=void 0,_ajax=(obj=_ajax)&&obj.__esModule?obj:{default:obj};_exports.getAvailableGateways=(component,paymentArea,itemId)=>{const request={methodname:"core_payment_get_available_gateways",args:{component:component,paymentarea:paymentArea,itemid:itemId}};return _ajax.default.call([request])[0]}}));

//# sourceMappingURL=repository.min.js.map