{"version": 3, "file": "repository.min.js", "sources": ["../src/repository.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Repository for payment subsystem.\n *\n * @module     core_payment/repository\n * @copyright  2020 Shamim Rezaie <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Ajax from 'core/ajax';\n\n/**\n * @typedef {Object} PaymentGateway A Payment Gateway\n * @property {string} shortname\n * @property {string} name\n * @property {string} description\n */\n\n/**\n * Returns the list of gateways that can process payments in the given currency.\n *\n * @method getAvailableGateways\n * @param {string} component\n * @param {string} paymentArea\n * @param {number} itemId\n * @returns {Promise<PaymentGateway[]>}\n */\nexport const getAvailableGateways = (component, paymentArea, itemId) => {\n    const request = {\n        methodname: 'core_payment_get_available_gateways',\n        args: {\n            component,\n            paymentarea: paymentArea,\n            itemid: itemId,\n        }\n    };\n    return Ajax.call([request])[0];\n};\n"], "names": ["component", "paymentArea", "itemId", "request", "methodname", "args", "paymentarea", "itemid", "Ajax", "call"], "mappings": ";;;;;;;oLAyCoC,CAACA,UAAWC,YAAaC,gBACnDC,QAAU,CACZC,WAAY,sCACZC,KAAM,CACFL,UAAAA,UACAM,YAAaL,YACbM,OAAQL,gBAGTM,cAAKC,KAAK,CAACN,UAAU"}