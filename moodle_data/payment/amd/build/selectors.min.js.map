{"version": 3, "file": "selectors.min.js", "sources": ["../src/selectors.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Define all of the selectors we will be using on the payment interface.\n *\n * @module     core_payment/selectors\n * @copyright  2019 Shamim <PERSON>zaie <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nexport default {\n    elements: {\n        gateways: '[data-region=\"gateways-container\"] input[type=\"radio\"]',\n    },\n    regions: {\n        gatewaysContainer: '[data-region=\"gateways-container\"]',\n        costContainer: '[data-region=\"fee-breakdown-container\"]',\n    },\n    values: {\n        gateway: '[data-region=\"gateways-container\"] input[type=\"radio\"]:checked',\n    },\n};\n"], "names": ["elements", "gateways", "regions", "gatewaysContainer", "costContainer", "values", "gateway"], "mappings": "wKAuBe,CACXA,SAAU,CACNC,SAAU,0DAEdC,QAAS,CACLC,kBAAmB,qCACnBC,cAAe,2CAEnBC,OAAQ,CACJC,QAAS"}