{"version": 3, "file": "gateways_modal.min.js", "sources": ["../src/gateways_modal.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * This module is responsible for PayPal content in the gateways modal.\n *\n * @module     paygw_paypal/gateways_modal\n * @copyright  2020 Shamim Rezaie <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport * as Repository from './repository';\nimport Templates from 'core/templates';\nimport Truncate from 'core/truncate';\nimport Modal from 'core/modal';\nimport ModalEvents from 'core/modal_events';\nimport {getString} from 'core/str';\n\n/**\n * Creates and shows a modal that contains a placeholder.\n *\n * @returns {Promise<Modal>}\n */\nconst showModalWithPlaceholder = async() => await Modal.create({\n    body: await Templates.render('paygw_paypal/paypal_button_placeholder', {}),\n    show: true,\n    removeOnClose: true,\n});\n\n/**\n * Process the payment.\n *\n * @param {string} component Name of the component that the itemId belongs to\n * @param {string} paymentArea The area of the component that the itemId belongs to\n * @param {number} itemId An internal identifier that is used by the component\n * @param {string} description Description of the payment\n * @returns {Promise<string>}\n */\nexport const process = (component, paymentArea, itemId, description) => {\n    return Promise.all([\n        showModalWithPlaceholder(),\n        Repository.getConfigForJs(component, paymentArea, itemId),\n    ])\n    .then(([modal, paypalConfig]) => {\n        return Promise.all([\n            modal,\n            paypalConfig,\n            switchSdk(paypalConfig.clientid, paypalConfig.currency),\n        ]);\n    })\n    .then(([modal, paypalConfig]) => {\n        // We have to clear the body. The render method in paypal.Buttons will render everything.\n        modal.setBody('');\n\n        return new Promise(resolve => {\n            window.paypal.Buttons({\n                // Set up the transaction.\n                createOrder: function(data, actions) {\n                    return actions.order.create({\n                        purchase_units: [{ // eslint-disable-line\n                            amount: {\n                                currency_code: paypalConfig.currency_code, // eslint-disable-line\n                                value: paypalConfig.cost,\n                            },\n                            description: Truncate.truncate(description, {length: 127, stripTags: true}),\n                        }],\n                        application_context: { // eslint-disable-line\n                            shipping_preference: 'NO_SHIPPING', // eslint-disable-line\n                            brand_name: Truncate.truncate(paypalConfig.brandname, {length: 127, stripTags: true}), // eslint-disable-line\n                        },\n                    });\n                },\n                // Finalise the transaction.\n                onApprove: function(data) {\n                    modal.getRoot().on(ModalEvents.outsideClick, (e) => {\n                        // Prevent closing the modal when clicking outside of it.\n                        e.preventDefault();\n                    });\n\n                    modal.setBody(getString('authorising', 'paygw_paypal'));\n\n                    Repository.markTransactionComplete(component, paymentArea, itemId, data.orderID)\n                    .then(res => {\n                        modal.hide();\n                        return res;\n                    })\n                    .then(resolve);\n                }\n            }).render(modal.getBody()[0]);\n        });\n    })\n    .then(res => {\n        if (res.success) {\n            return Promise.resolve(res.message);\n        }\n\n        return Promise.reject(res.message);\n    });\n};\n\n/**\n * Unloads the previously loaded PayPal JavaScript SDK, and loads a new one.\n *\n * @param {string} clientId PayPal client ID\n * @param {string} currency The currency\n * @returns {Promise}\n */\nconst switchSdk = (clientId, currency) => {\n    const sdkUrl = `https://www.paypal.com/sdk/js?client-id=${clientId}&currency=${currency}`;\n\n    // Check to see if this file has already been loaded. If so just go straight to the func.\n    if (switchSdk.currentlyloaded === sdkUrl) {\n        return Promise.resolve();\n    }\n\n    // PayPal can only work with one currency at the same time. We have to unload the previously loaded script\n    // if it was loaded for a different currency. Weird way indeed, but the only way.\n    // See: https://github.com/paypal/paypal-checkout-components/issues/1180\n    if (switchSdk.currentlyloaded) {\n        const suspectedScript = document.querySelector(`script[src=\"${switchSdk.currentlyloaded}\"]`);\n        if (suspectedScript) {\n            suspectedScript.parentNode.removeChild(suspectedScript);\n        }\n    }\n\n    const script = document.createElement('script');\n\n    return new Promise(resolve => {\n        if (script.readyState) {\n            script.onreadystatechange = function() {\n                if (this.readyState == 'complete' || this.readyState == 'loaded') {\n                    this.onreadystatechange = null;\n                    resolve();\n                }\n            };\n        } else {\n            script.onload = function() {\n                resolve();\n            };\n        }\n\n        script.setAttribute('src', sdkUrl);\n        document.head.appendChild(script);\n\n        switchSdk.currentlyloaded = sdkUrl;\n    });\n};\n\n/**\n * Holds the full url of loaded PayPal JavaScript SDK.\n *\n * @static\n * @type {string}\n */\nswitchSdk.currentlyloaded = '';\n"], "names": ["showModalWithPlaceholder", "async", "Modal", "create", "body", "Templates", "render", "show", "removeOnClose", "component", "paymentArea", "itemId", "description", "Promise", "all", "Repository", "getConfigForJs", "then", "_ref", "modal", "paypalConfig", "switchSdk", "clientid", "currency", "_ref2", "setBody", "resolve", "window", "paypal", "Buttons", "createOrder", "data", "actions", "order", "purchase_units", "amount", "currency_code", "value", "cost", "Truncate", "truncate", "length", "stripTags", "application_context", "shipping_preference", "brand_name", "brandname", "onApprove", "getRoot", "on", "ModalEvents", "outsideClick", "e", "preventDefault", "markTransactionComplete", "orderID", "res", "hide", "getBody", "success", "message", "reject", "clientId", "sdkUrl", "currentlyloaded", "suspectedScript", "document", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "script", "createElement", "readyState", "onreadystatechange", "this", "onload", "setAttribute", "head", "append<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;4MAmCMA,yBAA2BC,eAAiBC,eAAMC,OAAO,CAC3DC,WAAYC,mBAAUC,OAAO,yCAA0C,IACvEC,MAAM,EACNC,eAAe,qBAYI,CAACC,UAAWC,YAAaC,OAAQC,cAC7CC,QAAQC,IAAI,CACfd,2BACAe,WAAWC,eAAeP,UAAWC,YAAaC,UAErDM,MAAKC,WAAEC,MAAOC,0BACJP,QAAQC,IAAI,CACfK,MACAC,aACAC,UAAUD,aAAaE,SAAUF,aAAaG,eAGrDN,MAAKO,YAAEL,MAAOC,2BAEXD,MAAMM,QAAQ,IAEP,IAAIZ,SAAQa,UACfC,OAAOC,OAAOC,QAAQ,CAElBC,YAAa,SAASC,KAAMC,gBACjBA,QAAQC,MAAM9B,OAAO,CACxB+B,eAAgB,CAAC,CACbC,OAAQ,CACJC,cAAehB,aAAagB,cAC5BC,MAAOjB,aAAakB,MAExB1B,YAAa2B,kBAASC,SAAS5B,YAAa,CAAC6B,OAAQ,IAAKC,WAAW,MAEzEC,oBAAqB,CACjBC,oBAAqB,cACrBC,WAAYN,kBAASC,SAASpB,aAAa0B,UAAW,CAACL,OAAQ,IAAKC,WAAW,QAK3FK,UAAW,SAAShB,MAChBZ,MAAM6B,UAAUC,GAAGC,sBAAYC,cAAeC,IAE1CA,EAAEC,oBAGNlC,MAAMM,SAAQ,kBAAU,cAAe,iBAEvCV,WAAWuC,wBAAwB7C,UAAWC,YAAaC,OAAQoB,KAAKwB,SACvEtC,MAAKuC,MACFrC,MAAMsC,OACCD,OAEVvC,KAAKS,YAEXpB,OAAOa,MAAMuC,UAAU,UAGjCzC,MAAKuC,KACEA,IAAIG,QACG9C,QAAQa,QAAQ8B,IAAII,SAGxB/C,QAAQgD,OAAOL,IAAII,iBAW5BvC,UAAY,CAACyC,SAAUvC,kBACnBwC,yDAAoDD,8BAAqBvC,aAG3EF,UAAU2C,kBAAoBD,cACvBlD,QAAQa,aAMfL,UAAU2C,gBAAiB,OACrBC,gBAAkBC,SAASC,oCAA6B9C,UAAU2C,uBACpEC,iBACAA,gBAAgBG,WAAWC,YAAYJ,uBAIzCK,OAASJ,SAASK,cAAc,iBAE/B,IAAI1D,SAAQa,UACX4C,OAAOE,WACPF,OAAOG,mBAAqB,WACD,YAAnBC,KAAKF,YAA+C,UAAnBE,KAAKF,kBACjCC,mBAAqB,KAC1B/C,YAIR4C,OAAOK,OAAS,WACZjD,WAIR4C,OAAOM,aAAa,MAAOb,QAC3BG,SAASW,KAAKC,YAAYR,QAE1BjD,UAAU2C,gBAAkBD,WAUpC1C,UAAU2C,gBAAkB"}