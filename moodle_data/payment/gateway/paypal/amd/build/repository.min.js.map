{"version": 3, "file": "repository.min.js", "sources": ["../src/repository.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * PayPal repository module to encapsulate all of the AJAX requests that can be sent for PayPal.\n *\n * @module     paygw_paypal/repository\n * @copyright  2020 Shamim <PERSON>zaie <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Ajax from 'core/ajax';\n\n/**\n * Return the PayPal JavaScript SDK URL.\n *\n * @param {string} component Name of the component that the itemId belongs to\n * @param {string} paymentArea The area of the component that the itemId belongs to\n * @param {number} itemId An internal identifier that is used by the component\n * @returns {Promise<{clientid: string, brandname: string, cost: number, currency: string}>}\n */\nexport const getConfigForJs = (component, paymentArea, itemId) => {\n    const request = {\n        methodname: 'paygw_paypal_get_config_for_js',\n        args: {\n            component,\n            paymentarea: paymentArea,\n            itemid: itemId,\n        },\n    };\n\n    return Ajax.call([request])[0];\n};\n\n/**\n * Call server to validate and capture payment for order.\n *\n * @param {string} component Name of the component that the itemId belongs to\n * @param {string} paymentArea The area of the component that the itemId belongs to\n * @param {number} itemId An internal identifier that is used by the component\n * @param {string} orderId The order id coming back from PayPal\n * @returns {*}\n */\nexport const markTransactionComplete = (component, paymentArea, itemId, orderId) => {\n    const request = {\n        methodname: 'paygw_paypal_create_transaction_complete',\n        args: {\n            component,\n            paymentarea: paymentArea,\n            itemid: itemId,\n            orderid: orderId,\n        },\n    };\n\n    return Ajax.call([request])[0];\n};\n"], "names": ["component", "paymentArea", "itemId", "request", "methodname", "args", "paymentarea", "itemid", "Ajax", "call", "orderId", "orderid"], "mappings": ";;;;;;;yMAiC8B,CAACA,UAAWC,YAAaC,gBAC7CC,QAAU,CACZC,WAAY,iCACZC,KAAM,CACFL,UAAAA,UACAM,YAAaL,YACbM,OAAQL,gBAITM,cAAKC,KAAK,CAACN,UAAU,qCAYO,CAACH,UAAWC,YAAaC,OAAQQ,iBAC9DP,QAAU,CACZC,WAAY,2CACZC,KAAM,CACFL,UAAAA,UACAM,YAAaL,YACbM,OAAQL,OACRS,QAASD,iBAIVF,cAAKC,KAAK,CAACN,UAAU"}