{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_privacy/navigation

    Renders the Navigation section for the user export html page.
    This template is not for use within moodle.

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * navigation

    Example context (json):
    {
        "navigation": "Navigation html"
    }
}}
<div id="navigation" class="bg-light border">
    <div class="m-2">
        <div class="header">
            <div class="title">
                <h2>{{#str}}navigation, core_privacy{{/str}}</h2>
            </div>
        </div>
        <div class="content">
            {{{navigation}}}
        </div>
    </div>
</div>