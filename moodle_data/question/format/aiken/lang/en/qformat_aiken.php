<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Strings for component 'qformat_aiken', language 'en', branch 'MOODLE_20_STABLE'
 *
 * @package    qformat_aiken
 * @copyright  2010 Helen <PERSON>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['pluginname'] = 'Aiken format';
$string['pluginname_help'] = 'This is a simple format for importing multiple choice questions from a text file.';
$string['pluginname_link'] = 'qformat/aiken';
$string['privacy:metadata'] = 'The Aiken question format plugin does not store any personal data.';
$string['questionmissinganswers'] = 'Question must have at least 2 answers on line {$a}';
$string['questionnotcomplete'] = 'Question not completed before next question start on line {$a}';
$string['questionnotstarted'] = 'Question not started on line {$a}';
