<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Strings for component 'qformat_blackboard_six', language 'en', branch 'MOODLE_20_STABLE'
 *
 * @package    qformat_blackboard_six
 * @copyright  2010 Helen <PERSON>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['defaultname'] = 'Imported question {$a}';
$string['errormanifest'] = 'Error while parsing the IMS manifest document';
$string['importnotext'] = 'Missing question text in XML file';
$string['filenothandled'] = 'This archive contains reference to a file material {$a} which is not currently handled by import';
$string['imagenotfound'] = 'Image file with path {$a} was not found in the import.';
$string['importedcategory'] = 'Imported category {$a}';
$string['notenoughtsubans'] = 'Unable to import matching question \'{$a}\' because a matching question must comprise at least two questions and three answers.';
$string['pluginname'] = 'Blackboard';
$string['pluginname_help'] = 'Blackboard format enables questions saved in all Blackboard export formats to be imported via a dat or zip file. For zip files, images import is supported.';
$string['privacy:metadata'] = 'The Blackbard question format plugin does not store any personal data.';
$string['unhandledpresblock'] = 'Unhandled presentation block';
