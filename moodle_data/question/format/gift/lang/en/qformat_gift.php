<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Strings for component 'qformat_gift', language 'en', branch 'MOODLE_20_STABLE'
 *
 * @package    qformat_gift
 * @copyright  1999 onwards <PERSON>  {@link http://moodle.com}
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['braceerror'] = 'Could not find {...} around answers';
$string['giftleftbraceerror'] = 'Could not find a {';
$string['giftmatchingformat'] = 'Matching question answers are improperly formatted';
$string['giftnonumericalanswers'] = 'No answers found for numerical question';
$string['giftnovalidquestion'] = 'No valid question found';
$string['giftqtypenotset'] = 'Question type is not set';
$string['giftrightbraceerror'] = 'Could not find a }';
$string['importminerror'] = 'There is an error in the question. There are not enough answers for this question type';
$string['nohandler'] = 'No handler for question type {$a}';
$string['pluginname'] = 'GIFT format';
$string['pluginname_help'] = 'GIFT format enables multiple choice, true/false, short answer, matching, missing word, numerical and essay questions to be imported or exported via text file.';
$string['pluginname_link'] = 'qformat/gift';
$string['privacy:metadata'] = 'The GIFT question format plugin does not store any personal data.';
