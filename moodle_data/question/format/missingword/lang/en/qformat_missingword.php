<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON>le is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Strings for component 'qformat_missingword', language 'en', branch 'MOODLE_20_STABLE'
 *
 * @package    qformat_missingword
 * @copyright  2010 Helen <PERSON>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['pluginname'] = 'Missing word format';
$string['pluginname_help'] = 'Missing word format enables questions to be imported via text file.';
$string['pluginname_link'] = 'qformat/missingword';
$string['beginanswernotfound'] = 'Could not find a required "{" character in imported file content.';
$string['endanswernotfound'] = 'Could not find a required "}" character in imported file content.';
$string['noanswerfound'] = 'No answers found in question';
$string['privacy:metadata'] = 'The Missing word question format plugin does not store any personal data.';
