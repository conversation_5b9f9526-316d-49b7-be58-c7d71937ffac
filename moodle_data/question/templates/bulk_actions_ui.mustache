{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_question/bulk_actions_ui

    Example context (json):
    {
        "displaydata": [
          {
                "bulkactionitems": "<input value='Move to'>",
                "actionname": "Move to",
                "actionkey": "deleteselected",
                "actionurl": "/question/bank/bulkmove/move.php?courseid=2"
          }
        ]
    }
}}

<div class="btn-group dropup mt-2" id="bulkactionsui-container">
    <button type="button" class="btn btn-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" id="bulkactionsui-selector">
        {{#str}} withselected, question{{/str}}
    </button>
    <div class="dropdown-menu" aria-labelledby="bulkactionsui-selector">
        {{#bulkactionitems}}
            <input type="submit" value="{{actionname}}" class="dropdown-item" name="{{actionkey}}" data-action="toggle" data-togglegroup="qbank"
                   data-toggle="action" form="questionsubmit" formaction="{{{actionurl}}}" disabled="disabled">
        {{/bulkactionitems}}
    </div>
</div>
