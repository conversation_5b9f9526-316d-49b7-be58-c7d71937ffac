{{!
    This file is part of Moodle - http://moodle.org/

    <PERSON><PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_question/column_header

    Displays the <th> header cell for a column in the question bank.

    Context variables required for this template:
        * extraclasses: CSS classes to add to the th. E.g. "questionstatus pe-3".
        * help: (optional, may be omitted) help icon. The structure here needs to be as from help_icon::export_for_template().

        Other fields vary, depending on whether the column is sortable.

        Column with plain title, not sortable:
            * sortable: false
            * tiptitle: Column name (e.g. "Status")
            * sorttip: (optional) must be true if tip is present.
            * tip: (optional) a tool tip to show, if the title needs explanation. E.g. "Select questions for bulk actions".

        Sortable column, with a single type of sort (e.g. question type)
            * sortable: true
            * sortlinks: HTML of the title, in a link to sort by this. E.g. "<a href=\"http:\/\/localhost\/moodle_head\/question\/edit.php?courseid=100000&amp;qbs1=-qbank_viewquestiontype%5Cquestion_type_column&amp;qbs2=qbank_viewquestionname%5Cquestion_name_idnumber_tags_column-name\" title=\"Sort by Question type descending\">\n    T<i class=\"icon fa fa-sort-asc fa-fw\"  title=\"Ascending\" role=\"img\" aria-label=\"Ascending\"><\/i>\n<\/a>"

        Sortable column, with an overall title, and multiple sort types (e.g. question name/idnumber)
            * sortable: true
            * title: "Question"
            * sortlinks: Several sort links concatenated. E.g. "<a href=\"http:\/\/localhost\/moodle_head\/question\/edit.php?courseid=100000&amp;qbs1=qbank_viewquestionname%5Cquestion_name_idnumber_tags_column-name&amp;qbs2=qbank_viewquestiontype%5Cquestion_type_column\" title=\"Sort by Question name ascending\">\n    Question name\n<\/a> \/ <a href=\"http:\/\/localhost\/moodle_head\/question\/edit.php?courseid=100000&amp;qbs1=qbank_viewquestionname%5Cquestion_name_idnumber_tags_column-idnumber&amp;qbs2=qbank_viewquestiontype%5Cquestion_type_column&amp;qbs3=qbank_viewquestionname%5Cquestion_name_idnumber_tags_column-name\" title=\"Sort by ID number ascending\">\n    ID number\n<\/a>"

    Example context (json):
    {
        "sortable": false,
        "extraclasses": "checkbox pe-1",
        "tiptitle": "<input id=\"qbheadercheckbox\" name=\"qbheadercheckbox\" type=\"checkbox\"  value=\"1\" data-action=\"toggle\" data-toggle=\"master\" data-togglegroup=\"qbank\" data-toggle-selectall=\"Select all\" data-toggle-deselectall=\"Deselect all\"><label for=\"qbheadercheckbox\" class=\"accesshide\">Select all<\/label>",
        "sorttip": true,
        "tip": "Select questions for bulk actions",
        "class": "plugin_class",
        "name": "plugin_name"
    }
}}
<th class="header align-top {{extraclasses}}"
    scope="col"
    data-pluginname="{{class}}"
    data-name="{{name}}"
    data-columnid="{{columnid}}"
    {{#width}}style="width: {{width}};"{{/width}}>
    <div class="header-container">
        <div class="header-text">
        {{#title}}
            <div class="title me-1">{{title}}</div>
        {{/title}}
        {{^sortable}}
            {{^sorttip}}
                <span class="me-1">{{tiptitle}}</span>
            {{/sorttip}}
            {{#sorttip}}
                <span class="me-1" title="{{tip}}">{{{tiptitle}}}</span>
            {{/sorttip}}
        {{/sortable}}
        {{#help}}
            {{>core/help_icon}}
        {{/help}}
        {{#actionmenu}}{{>core/action_menu}}{{/actionmenu}}
        </div>
    </div>
    {{#sortable}}
        <div class="sorters">
            {{{sortlinks}}}
        </div>
    {{/sortable}}
</th>
