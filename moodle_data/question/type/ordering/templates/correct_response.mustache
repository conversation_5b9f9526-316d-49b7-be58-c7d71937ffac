{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template qtype_ordering/correct_response

    Renders the description of the correct response to a given question attempt.

    Context variables required for this template:
    * hascorrectresponse - Whether correct response exist.
    * showcorrect - Whether to show the correct response.
    * orderinglayoutclass - The ordering layout CSS class.
    * correctanswers - An array containing the correct answers.
        * answertext - The formatted answer text.

    Example context (json):
    {
        "hascorrectresponse": true,
        "showcorrect": "true",
        "orderinglayoutclass": "vertical",
        "correctanswers": [
            {
                "answertext": "Correct answer 1"
            },
            {
                "answertext": "Correct answer 2"
            }
        ]
    }
}}

{{#hascorrectresponse}}
    {{#showcorrect}}
        <p>{{#str}} correctorder, qtype_ordering {{/str}}</p>
        <ol class="correctorder {{orderinglayoutclass}}">
            {{#correctanswers}}
                <li class="{{orderinglayoutclass}}">
                    {{answertext}}
                </li>
            {{/correctanswers}}
        </ol>
    {{/showcorrect}}
{{/hascorrectresponse}}
{{^hascorrectresponse}}
    <p>{{#str}} noresponsedetails, qtype_ordering {{/str}}</p>
{{/hascorrectresponse}}
