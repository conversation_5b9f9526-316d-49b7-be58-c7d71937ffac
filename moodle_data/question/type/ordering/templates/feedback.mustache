{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template qtype_ordering/feedback

    Renders the feedback for the question.

    Context variables required for this template:
    * specificfeedback - Any specific feedback for the question.
    * numpartscorrect - The number of parts correct.
    * specificgradedetailfeedback - The specific grade detail feedback.
    * hint - The hint for the question.
    * generalfeedback - The general feedback for the question.
    * rightanswer - The right answer for the question.

    Example context (json):
    {
        "specificfeedback": "Maybe you should try again.",
        "numpartscorrect": {
            "numcorrect": 1,
            "numpartial": 3,
            "numincorrect": 0
        },
        "specificgradedetailfeedback": {
            "showpartialwrong": true,
            "gradingtype": "Grading type: Absolute position",
            "gradedetails": "93",
            "orderinglayoutclass": "vertical",
            "scoredetails": [
                {
                    "score": "1",
                    "maxscore": "1",
                    "percent": "100"
                },
                {
                    "score": "0",
                    "maxscore": "1",
                    "percent": "0"
                }
            ]
        },
        "hint": "The cake is a lie.",
        "generalfeedback": "Here is some general feedback.",
        "rightanswer": {
            "hascorrectresponse": true,
            "showcorrect": "true",
            "orderinglayoutclass": "vertical",
            "correctanswers": [
                {
                    "answertext": "Correct answer 1"
                },
                {
                    "answertext": "Correct answer 2"
                }
            ]
        }
    }
}}
{{#specificfeedback}}
    <div class="specificfeedback">
        {{{.}}}
    </div>
{{/specificfeedback}}
{{#numpartscorrect}}
    <div class="numpartscorrect">
        {{>qtype_ordering/num_parts_correct}}
    </div>
{{/numpartscorrect}}
{{#specificgradedetailfeedback}}
    {{>qtype_ordering/specific_grade_detail_feedback}}
{{/specificgradedetailfeedback}}
{{#hint}}
    <div class="hint">
        {{{.}}}
    </div>
{{/hint}}
{{#generalfeedback}}
    <div class="generalfeedback">
        {{{.}}}
    </div>
{{/generalfeedback}}
{{#rightanswer}}
    <div class="rightanswer">
        {{>qtype_ordering/correct_response}}
    </div>
{{/rightanswer}}
