{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template qtype_ordering/num_parts_correct

    Renders a statement of how many sub-parts of the question the student got correct|partial|incorrect.

    Context variables required for this template:
    * numcorrect - The number of correct answers.
    * numpartial - The number of partially correct answers.
    * numincorrect - The number of incorrect answers.

    Example context (json):
    {
        "numcorrect": 1,
        "numpartial": 3,
        "numincorrect": 0
    }
}}

{{#numcorrect}}
    <div>
        {{#str}} correctitemsnumber, qtype_ordering, {{numcorrect}} {{/str}}
    </div>
{{/numcorrect}}
{{#numpartial}}
    <div>
        {{#str}} partialitemsnumber, qtype_ordering, {{numpartial}} {{/str}}
    </div>
{{/numpartial}}
{{#numincorrect}}
    <div>
        {{#str}} incorrectitemsnumber, qtype_ordering, {{numincorrect}} {{/str}}
    </div>
{{/numincorrect}}
