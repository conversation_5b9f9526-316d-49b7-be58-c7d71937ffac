<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Strings for component 'qtype_truefalse', language 'en', branch 'MOODLE_20_STABLE'
 *
 * @package    qtype
 * @subpackage truefalse
 * @copyright  1999 onwards <PERSON>  {@link http://moodle.com}
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['correctanswer'] = 'Correct answer';
$string['correctanswerfalse'] = 'The correct answer is \'False\'.';
$string['correctanswertrue'] = 'The correct answer is \'True\'.';
$string['false'] = 'False';
$string['feedbackfalse'] = 'Feedback for the response \'False\'.';
$string['feedbacktrue'] = 'Feedback for the response \'True\'.';
$string['pleaseselectananswer'] = 'Please select an answer.';
$string['selectone'] = 'Select one:';
$string['true'] = 'True';
$string['pluginname'] = 'True/False';
$string['pluginname_help'] = 'In response to a question (that may include an image) the respondent chooses from true or false.';
$string['pluginname_link'] = 'question/type/truefalse';
$string['pluginnameadding'] = 'Adding a True/False question';
$string['pluginnameediting'] = 'Editing a True/False question';
$string['pluginnamesummary'] = 'A simple form of multiple choice question with just the two choices \'True\' and \'False\'.';
$string['privacy:preference:showstandardinstruction'] = 'Whether showing standard instruction.';
$string['showstandardinstruction'] = 'Show standard instructions';
$string['showstandardinstruction_help'] = 'Whether to show the instructions "Select one:" before true/false answers.';
